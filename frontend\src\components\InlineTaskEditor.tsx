import React, { useState } from 'react';
import {
  Save,
  X,
  Plus,
  Trash2,
  Clock,
  Zap,
  AlertTriangle,
  Minus
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

interface InlineTaskEditorProps {
  task: Task;
  onSave: () => void;
  onCancel: () => void;
}

const priorityOptions = [
  { id: 'LOW', label: 'Low', color: 'bg-secondary-100 text-secondary-700', icon: Minus },
  { id: 'MEDIUM', label: 'Medium', color: 'bg-primary-100 text-primary-700', icon: Clock },
  { id: 'HIGH', label: 'High', color: 'bg-warning-100 text-warning-700', icon: Zap },
  { id: 'URGENT', label: 'Urgent', color: 'bg-error-100 text-error-700', icon: AlertTriangle },
];



const InlineTaskEditor: React.FC<InlineTaskEditorProps> = ({ task, onSave, onCancel }) => {
  const { updateTask, createTask, deleteTask } = useTaskStore();
  const { projects } = useProjectStore();

  const [formData, setFormData] = useState({
    title: task.title,
    content: task.content || '',
    priority: task.priority || 'MEDIUM',
    effortEstimate: task.effortEstimate || '',
    dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '',
    projectId: task.projectId || '',
  });

  const [subtasks, setSubtasks] = useState<Task[]>(task.subTasks || []);
  const [newSubtask, setNewSubtask] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!formData.title.trim()) return;

    setIsLoading(true);
    try {
      await updateTask(task.id, {
        title: formData.title,
        content: formData.content || undefined,
        priority: formData.priority as 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT',
        effortEstimate: formData.effortEstimate ? parseInt(formData.effortEstimate.toString()) : undefined,
        dueDate: formData.dueDate ? new Date(formData.dueDate).toISOString() : undefined,
        projectId: formData.projectId || undefined,
      });
      onSave();
    } catch (error) {
      console.error('Failed to update task:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddSubtask = async () => {
    if (!newSubtask.trim()) return;

    try {
      const subtask = await createTask({
        title: newSubtask,
        parentId: task.id,
        projectId: task.projectId,
      });
      setSubtasks([...subtasks, subtask]);
      setNewSubtask('');
    } catch (error) {
      console.error('Failed to create subtask:', error);
    }
  };

  const handleDeleteSubtask = async (subtaskId: string) => {
    try {
      await deleteTask(subtaskId);
      setSubtasks(subtasks.filter(st => st.id !== subtaskId));
    } catch (error) {
      console.error('Failed to delete subtask:', error);
    }
  };

  return (
    <Card className="border-2 border-primary-200 shadow-lg">
      <CardContent className="p-4 space-y-4">
        {/* Title */}
        <div>
          <Input
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            placeholder="Task title..."
            className="text-lg font-medium"
            autoFocus
          />
        </div>

        {/* Content */}
        <div>
          <textarea
            value={formData.content}
            onChange={(e) => setFormData({ ...formData, content: e.target.value })}
            placeholder="Add description..."
            className="w-full p-2 border border-secondary-300 rounded-md resize-none"
            rows={3}
          />
        </div>

        {/* Properties Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Priority */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Priority
            </label>
            <div className="flex flex-wrap gap-2">
              {priorityOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <button
                    key={option.id}
                    onClick={() => setFormData({ ...formData, priority: option.id as 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' })}
                    className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-all ${
                      formData.priority === option.id
                        ? option.color + ' ring-2 ring-primary-400'
                        : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
                    }`}
                  >
                    <Icon className="h-3 w-3" />
                    <span>{option.label}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Effort Estimate */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Effort (minutes)
            </label>
            <Input
              type="number"
              value={formData.effortEstimate}
              onChange={(e) => setFormData({ ...formData, effortEstimate: e.target.value })}
              placeholder="30"
              min="1"
              max="1440"
            />
          </div>

          {/* Due Date */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Due Date
            </label>
            <Input
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
            />
          </div>

          {/* Project */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Project
            </label>
            <select
              value={formData.projectId}
              onChange={(e) => setFormData({ ...formData, projectId: e.target.value })}
              className="w-full p-2 border border-secondary-300 rounded-md"
            >
              <option value="">No Project</option>
              {projects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Subtasks */}
        <div>
          <label className="block text-sm font-medium text-secondary-700 mb-2">
            Subtasks ({subtasks.length})
          </label>

          {/* Existing Subtasks */}
          <div className="space-y-2 mb-3">
            {subtasks.map((subtask) => (
              <div key={subtask.id} className="flex items-center space-x-2 p-2 bg-secondary-50 rounded">
                <span className="flex-1 text-sm">{subtask.title}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteSubtask(subtask.id)}
                  className="text-error-600 hover:text-error-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>

          {/* Add New Subtask */}
          <div className="flex space-x-2">
            <Input
              value={newSubtask}
              onChange={(e) => setNewSubtask(e.target.value)}
              placeholder="Add subtask..."
              className="flex-1"
              onKeyPress={(e) => e.key === 'Enter' && handleAddSubtask()}
            />
            <Button
              onClick={handleAddSubtask}
              disabled={!newSubtask.trim()}
              size="sm"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button
            variant="ghost"
            onClick={onCancel}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-1" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!formData.title.trim() || isLoading}
          >
            <Save className="h-4 w-4 mr-1" />
            {isLoading ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default InlineTaskEditor;
