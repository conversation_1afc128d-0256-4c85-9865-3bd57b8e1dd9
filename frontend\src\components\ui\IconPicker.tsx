import React, { useState } from 'react';
import {
  Search,
  X,
  Star,
  Heart,
  Zap,
  Target,
  Clock,
  Calendar,
  CheckCircle,
  AlertCircle,
  Info,
  Settings,
  User,
  Users,
  Home,
  Building,
  Car,
  Plane,
  Coffee,
  Book,
  Briefcase,
  ShoppingCart,
  Dumbbell,
  Music,
  Camera,
  Gamepad2,
  Palette,
  Code,
  Database,
  Globe,
  Mail,
  Phone,
  MessageCircle,
  FileText,
  Folder,
  Archive,
  Trash2,
  Edit,
  Plus,
  Minus,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  MoreVertical,
} from 'lucide-react';
import Button from '@/components/ui/Button';

// Available icons for tags
const AVAILABLE_ICONS = [
  { name: 'Star', icon: Star, category: 'General' },
  { name: 'Heart', icon: Heart, category: 'General' },
  { name: 'Zap', icon: Zap, category: 'General' },
  { name: 'Target', icon: Target, category: 'General' },
  { name: 'Clock', icon: Clock, category: 'Time' },
  { name: 'Calendar', icon: Calendar, category: 'Time' },
  { name: 'CheckCircle', icon: CheckCircle, category: 'Status' },
  { name: 'AlertCircle', icon: AlertCircle, category: 'Status' },
  { name: 'Info', icon: Info, category: 'Status' },
  { name: 'Settings', icon: Settings, category: 'Tools' },
  { name: 'User', icon: User, category: 'People' },
  { name: 'Users', icon: Users, category: 'People' },
  { name: 'Home', icon: Home, category: 'Places' },
  { name: 'Building', icon: Building, category: 'Places' },
  { name: 'Car', icon: Car, category: 'Transport' },
  { name: 'Plane', icon: Plane, category: 'Transport' },
  { name: 'Coffee', icon: Coffee, category: 'Lifestyle' },
  { name: 'Book', icon: Book, category: 'Education' },
  { name: 'Briefcase', icon: Briefcase, category: 'Work' },
  { name: 'ShoppingCart', icon: ShoppingCart, category: 'Shopping' },
  { name: 'Dumbbell', icon: Dumbbell, category: 'Health' },
  { name: 'Music', icon: Music, category: 'Entertainment' },
  { name: 'Camera', icon: Camera, category: 'Entertainment' },
  { name: 'Gamepad2', icon: Gamepad2, category: 'Entertainment' },
  { name: 'Palette', icon: Palette, category: 'Creative' },
  { name: 'Code', icon: Code, category: 'Tech' },
  { name: 'Database', icon: Database, category: 'Tech' },
  { name: 'Globe', icon: Globe, category: 'Tech' },
  { name: 'Mail', icon: Mail, category: 'Communication' },
  { name: 'Phone', icon: Phone, category: 'Communication' },
  { name: 'MessageCircle', icon: MessageCircle, category: 'Communication' },
  { name: 'FileText', icon: FileText, category: 'Files' },
  { name: 'Folder', icon: Folder, category: 'Files' },
  { name: 'Archive', icon: Archive, category: 'Files' },
];

const CATEGORIES = [
  'All',
  'General',
  'Time',
  'Status',
  'Tools',
  'People',
  'Places',
  'Transport',
  'Lifestyle',
  'Education',
  'Work',
  'Shopping',
  'Health',
  'Entertainment',
  'Creative',
  'Tech',
  'Communication',
  'Files',
];

interface IconPickerProps {
  selectedIcon?: string;
  onIconSelect: (iconName: string) => void;
  onClose: () => void;
}

const IconPicker: React.FC<IconPickerProps> = ({
  selectedIcon,
  onIconSelect,
  onClose,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const filteredIcons = AVAILABLE_ICONS.filter((iconData) => {
    const matchesSearch = iconData.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || iconData.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleIconSelect = (iconName: string) => {
    onIconSelect(iconName);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-secondary-200">
          <h3 className="text-lg font-semibold text-secondary-900">Choose an Icon</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Search and Filter */}
        <div className="p-4 border-b border-secondary-200">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
            <input
              type="text"
              placeholder="Search icons..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {CATEGORIES.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-3 py-1 text-xs rounded-full transition-colors ${
                  selectedCategory === category
                    ? 'bg-primary-100 text-primary-700 border border-primary-300'
                    : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Icon Grid */}
        <div className="p-4 overflow-y-auto max-h-96">
          <div className="grid grid-cols-8 gap-2">
            {filteredIcons.map((iconData) => {
              const IconComponent = iconData.icon;
              const isSelected = selectedIcon === iconData.name;

              return (
                <button
                  key={iconData.name}
                  onClick={() => handleIconSelect(iconData.name)}
                  className={`p-3 rounded-lg border-2 transition-all hover:scale-105 ${
                    isSelected
                      ? 'border-primary-500 bg-primary-50 text-primary-600'
                      : 'border-secondary-200 hover:border-secondary-300 text-secondary-600 hover:text-secondary-800'
                  }`}
                  title={iconData.name}
                >
                  <IconComponent className="h-5 w-5 mx-auto" />
                </button>
              );
            })}
          </div>

          {filteredIcons.length === 0 && (
            <div className="text-center py-8 text-secondary-500">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No icons found matching your search.</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-secondary-200 bg-secondary-50">
          <p className="text-xs text-secondary-600 text-center">
            {filteredIcons.length} icon{filteredIcons.length !== 1 ? 's' : ''} available
          </p>
        </div>
      </div>
    </div>
  );
};

export default IconPicker;
