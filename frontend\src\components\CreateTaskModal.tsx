import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CheckSquare } from 'lucide-react';
import { Project } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

const createTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required').max(200, 'Task title must be less than 200 characters'),
  content: z.string().optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  dueDate: z.string().optional(),
  effortEstimate: z.number().min(1).max(1440).optional(),
  energyLevel: z.enum(['LOW', 'MEDIUM', 'HIGH']).optional(),
  projectId: z.string().optional(),
});

type CreateTaskFormData = z.infer<typeof createTaskSchema>;

interface CreateTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  projects: Project[];
  defaultProjectId?: string;
  defaultDueDate?: Date | null;
  onSuccess?: () => void;
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({
  isOpen,
  onClose,
  projects,
  defaultProjectId,
  defaultDueDate,
  onSuccess,
}) => {
  const { createTask } = useTaskStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateTaskFormData>({
    resolver: zodResolver(createTaskSchema),
    defaultValues: {
      projectId: defaultProjectId,
      priority: 'MEDIUM',
      energyLevel: 'MEDIUM',
      dueDate: defaultDueDate ? defaultDueDate.toISOString().slice(0, 16) : undefined,
    },
  });

  const onSubmit = async (data: CreateTaskFormData) => {
    try {
      setIsSubmitting(true);

      const taskData = {
        title: data.title,
        content: data.content,
        priority: data.priority,
        dueDate: data.dueDate ? new Date(data.dueDate).toISOString() : undefined,
        effortEstimate: data.effortEstimate,
        energyLevel: data.energyLevel,
        projectId: data.projectId,
      };

      await createTask(taskData);

      reset();
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error('Failed to create task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Task"
      size="lg"
    >
      <div className="space-y-6">
        {/* Task Icon */}
        <div className="flex items-center justify-center">
          <div className="p-4 bg-primary-100 rounded-xl">
            <CheckSquare className="h-8 w-8 text-primary-600" />
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Task Title */}
          <Input
            label="Task Title *"
            {...register('title')}
            error={errors.title?.message}
            placeholder="What needs to be done?"
            autoFocus
          />

          {/* Description */}
          <div className="space-y-1">
            <label className="block text-sm font-medium text-secondary-700">
              Description
            </label>
            <textarea
              {...register('content')}
              rows={3}
              className="input resize-none"
              placeholder="Additional details about this task..."
            />
            {errors.content && (
              <p className="text-sm text-error-600">{errors.content.message}</p>
            )}
          </div>

          {/* Project */}
          <div className="space-y-1">
            <label className="block text-sm font-medium text-secondary-700">
              Project
            </label>
            <select
              {...register('projectId')}
              className="input"
            >
              <option value="">No project</option>
              {projects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>

          {/* Priority and Energy Level */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <label className="block text-sm font-medium text-secondary-700">
                Priority
              </label>
              <select
                {...register('priority')}
                className="input"
              >
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="URGENT">Urgent</option>
              </select>
            </div>

            <div className="space-y-1">
              <label className="block text-sm font-medium text-secondary-700">
                Energy Level
              </label>
              <select
                {...register('energyLevel')}
                className="input"
              >
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
              </select>
            </div>
          </div>

          {/* Due Date and Effort Estimate */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Due Date"
              type="datetime-local"
              {...register('dueDate')}
              error={errors.dueDate?.message}
            />

            <Input
              label="Effort Estimate (minutes)"
              type="number"
              min="1"
              max="1440"
              {...register('effortEstimate', { valueAsNumber: true })}
              error={errors.effortEstimate?.message}
              placeholder="e.g., 30"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isSubmitting}
            >
              Create Task
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default CreateTaskModal;
