1. User Creates a Project: 'Example House, Car, Work, Daily, Personal, Shopping'
2. User Writes Thoughts on the fly: 'Call Destist', 'Paint Bathroom', 'Get new boots'
3. User Drags the 'Call Destist' thought into the 'Personal - High Priority' Project folder, and it becomes a task with a priority.
4. User can VIEW ALL 'High and Urgent Priority' tasks on the dashboard and view todays created Schedule, with the ability to change from todays date, and see the time slots for each task scheduled.
5. User can select 'Timeline' and view and change all tasks in a timeline view, with the ability to filter by hourly or daily seemlessly and with incredible performance. The user can extend tasks timelines, drag and drop tasks to reschedule.

- User can edit tasks and modify them from these pages. No extra page or bloat needed or extremely complicated booleans for each task.

SO

1. Project is Created with Name and Color = (ProjectName + Color Selected)
2. Thought is Captured with Name = (QuickAdd-ThoughtName)
3. Thought becomes Task with Priority when drag and dropped to its project + priority selection = (QuickAdd-ThoughtName ---> TaskName + Priority) = (TaskName + Priority + ProjectName + Color Selected) The Task will automatically be created as a 'Not Scheduled' Task until it is scheduled through the Dashboard or timeline.

4. 
5.
6.
7.
