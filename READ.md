1. User Creates a Project: 'Example House, Car, Work, Daily, Personal, Shopping'
2. User Writes Thoughts on the fly: 'Call Destist', 'Paint Bathroom', 'Get new boots'
3. User Drags the 'Call Destist' thought into the 'Personal - High Priority' Project folder, and it becomes a task with a priority.
4. User can VIEW ALL 'High and Urgent Priority' tasks on the dashboard and view todays created Schedule, with the ability to change from todays date, and see the time slots for each task scheduled.
5. User can select 'Timeline' and view and change all tasks in a timeline view, with the ability to filter by hourly or daily seemlessly and with incredible performance. The user can extend tasks timelines, drag and drop tasks to reschedule.

- User can edit tasks and modify them from these pages. No extra page or bloat needed or extremely complicated booleans for each task.

SO

1. Project Created (Project1) with Priorities + Name (Projec) (LOW, MEDIUM, HIGH, URGENT)
2. Thought is Captured (Thought1) with Name
3. Thought becomes Task with Priority
4. View All High and Urgent Priority Tasks on Dashboard
5. View Today's Schedule and Change Date
6. View All Tasks in Timeline View and Filter by Hourly or Daily
7. Edit Tasks from these pages
