import React, { useEffect, useState } from 'react';
import { Columns, Plus, Filter, Search } from 'lucide-react';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';

import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import KanbanBoard from '@/components/KanbanBoard';
import CreateTaskModal from '@/components/CreateTaskModal';
import QuickAdd from '@/components/QuickAdd';

const BoardPage: React.FC = () => {
  const { tasks, fetchTasks } = useTaskStore();
  const { projects, fetchProjects } = useProjectStore();
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchTasks();
    fetchProjects();
  }, [fetchTasks, fetchProjects]);

  // Filter tasks by project and search query
  const filteredTasks = tasks.filter(task => {
    const matchesProject = selectedProject === 'all' || task.project?.id === selectedProject;
    const matchesSearch = searchQuery === '' ||
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.content?.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesProject && matchesSearch;
  });

  const handleTaskCreate = () => {
    setShowCreateTaskModal(true);
  };

  const handleTaskCreated = () => {
    fetchTasks();
  };

  const getStatusCounts = () => {
    return {
      TODO: filteredTasks.filter(t => t.status === 'TODO').length,
      IN_PROGRESS: filteredTasks.filter(t => t.status === 'IN_PROGRESS').length,
      DONE: filteredTasks.filter(t => t.status === 'DONE').length,
      ARCHIVED: filteredTasks.filter(t => t.status === 'ARCHIVED').length,
    };
  };

  const getPriorityStats = () => {
    return {
      URGENT: filteredTasks.filter(t => t.priority === 'URGENT').length,
      HIGH: filteredTasks.filter(t => t.priority === 'HIGH').length,
      MEDIUM: filteredTasks.filter(t => t.priority === 'MEDIUM').length,
      LOW: filteredTasks.filter(t => t.priority === 'LOW').length,
    };
  };

  const statusCounts = getStatusCounts();
  const priorityStats = getPriorityStats();

  return (
    <div className="h-screen flex flex-col bg-secondary-50">
      {/* Header */}
      <div className="bg-white border-b border-secondary-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-100 rounded-lg">
              <Columns className="h-6 w-6 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">Board</h1>
              <p className="text-secondary-600">
                {filteredTasks.length} task{filteredTasks.length !== 1 ? 's' : ''} across all projects
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
              <Input
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>

            {/* Project Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-secondary-500" />
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="input min-w-[150px]"
              >
                <option value="all">All Projects</option>
                {projects.map(project => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
            </div>

            <Button onClick={handleTaskCreate}>
              <Plus className="h-4 w-4 mr-2" />
              New Task
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="bg-white border-b border-secondary-200 px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Status Stats */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-secondary-400 rounded-full"></div>
              <span className="text-sm text-secondary-600">To Do: {statusCounts.TODO}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
              <span className="text-sm text-secondary-600">In Progress: {statusCounts.IN_PROGRESS}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-success-500 rounded-full"></div>
              <span className="text-sm text-secondary-600">Done: {statusCounts.DONE}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-secondary-300 rounded-full"></div>
              <span className="text-sm text-secondary-600">Archived: {statusCounts.ARCHIVED}</span>
            </div>
          </div>

          {/* Priority Stats */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-error-500 rounded-full"></div>
              <span className="text-sm text-secondary-600">Urgent: {priorityStats.URGENT}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-warning-500 rounded-full"></div>
              <span className="text-sm text-secondary-600">High: {priorityStats.HIGH}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
              <span className="text-sm text-secondary-600">Medium: {priorityStats.MEDIUM}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-secondary-400 rounded-full"></div>
              <span className="text-sm text-secondary-600">Low: {priorityStats.LOW}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Board Content */}
      <div className="flex-1 p-6 min-h-0">
        <KanbanBoard
          tasks={filteredTasks}
          onTaskCreate={handleTaskCreate}
        />
      </div>

      {/* Create Task Modal */}
      <CreateTaskModal
        isOpen={showCreateTaskModal}
        onClose={() => setShowCreateTaskModal(false)}
        projects={projects}
        defaultProjectId={selectedProject !== 'all' ? selectedProject : undefined}
        onSuccess={handleTaskCreated}
      />

      <QuickAdd />
    </div>
  );
};

export default BoardPage;
