import React, { useState, useMemo } from 'react';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Plus } from 'lucide-react';
import { Task } from '@/services/api';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isToday, isSameMonth, addMonths, subMonths } from 'date-fns';

interface CalendarViewProps {
  tasks: Task[];
  onTaskCreate?: (date: Date) => void;
  onTaskClick?: (task: Task) => void;
}

const CalendarView: React.FC<CalendarViewProps> = ({
  tasks,
  onTaskCreate,
  onTaskClick,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Group tasks by date
  const tasksByDate = useMemo(() => {
    const grouped: Record<string, Task[]> = {};

    tasks.forEach(task => {
      if (task.dueDate) {
        const dateKey = format(new Date(task.dueDate), 'yyyy-MM-dd');
        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push(task);
      }
    });

    return grouped;
  }, [tasks]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev =>
      direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1)
    );
  };

  const getTasksForDate = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    return tasksByDate[dateKey] || [];
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-error-500';
      case 'HIGH': return 'bg-warning-500';
      case 'MEDIUM': return 'bg-primary-500';
      case 'LOW': return 'bg-secondary-400';
      default: return 'bg-secondary-300';
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-5 w-5 text-primary-600" />
            <h2 className="text-xl font-semibold text-secondary-900">
              {format(currentDate, 'MMMM yyyy')}
            </h2>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('prev')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentDate(new Date())}
          >
            Today
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('next')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="flex-1 p-4">
        {/* Days of Week Header */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-2 text-center text-sm font-medium text-secondary-600">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1 h-full">
          {calendarDays.map(date => {
            const dayTasks = getTasksForDate(date);
            const isCurrentMonth = isSameMonth(date, currentDate);
            const isCurrentDay = isToday(date);

            return (
              <Card
                key={date.toISOString()}
                className={`min-h-[120px] transition-colors hover:shadow-soft-md ${
                  !isCurrentMonth ? 'opacity-50' : ''
                } ${isCurrentDay ? 'ring-2 ring-primary-500' : ''}`}
              >
                <CardContent className="p-2 h-full flex flex-col">
                  {/* Date Number */}
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${
                      isCurrentDay ? 'text-primary-600' : 'text-secondary-900'
                    }`}>
                      {format(date, 'd')}
                    </span>

                    {onTaskCreate && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onTaskCreate(date)}
                        className="p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  {/* Tasks */}
                  <div className="flex-1 space-y-1 overflow-hidden">
                    {dayTasks.slice(0, 3).map(task => (
                      <div
                        key={task.id}
                        onClick={() => onTaskClick?.(task)}
                        className="cursor-pointer group"
                      >
                        <div className="flex items-center space-x-2 p-1 rounded hover:bg-secondary-50">
                          <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                            getPriorityColor(task.priority || 'LOW')
                          }`} />
                          <span className="text-xs text-secondary-700 truncate">
                            {task.title}
                          </span>
                        </div>
                      </div>
                    ))}

                    {dayTasks.length > 3 && (
                      <div className="text-xs text-secondary-500 text-center">
                        +{dayTasks.length - 3} more
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default CalendarView;
