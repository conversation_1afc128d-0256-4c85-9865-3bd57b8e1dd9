import React, { useEffect, useState } from 'react';
import { Calendar as CalendarIcon, Plus, Filter } from 'lucide-react';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import CalendarView from '@/components/CalendarView';
import CreateTaskModal from '@/components/CreateTaskModal';
import QuickAdd from '@/components/QuickAdd';
import { Task } from '@/services/api';

const CalendarPage: React.FC = () => {
  const { tasks, fetchTasks } = useTaskStore();
  const { projects, fetchProjects } = useProjectStore();
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedProject, setSelectedProject] = useState<string>('all');

  useEffect(() => {
    fetchTasks();
    fetchProjects();
  }, [fetchTasks, fetchProjects]);

  // Filter tasks that have due dates
  const scheduledTasks = tasks.filter(task => task.dueDate);

  // Filter by project if selected
  const filteredTasks = selectedProject === 'all'
    ? scheduledTasks
    : scheduledTasks.filter(task => task.project?.id === selectedProject);

  const handleTaskCreate = (date?: Date) => {
    setSelectedDate(date || null);
    setShowCreateTaskModal(true);
  };

  const handleTaskCreated = () => {
    fetchTasks();
  };

  const handleTaskClick = (task: Task) => {
    // TODO: Open task details modal
    console.log('Task clicked:', task);
  };

  const getProjectStats = () => {
    const stats = projects.map(project => {
      const projectTasks = scheduledTasks.filter(task => task.project?.id === project.id);
      return {
        project,
        taskCount: projectTasks.length,
        completedCount: projectTasks.filter(task => task.status === 'DONE').length,
      };
    });
    return stats;
  };

  return (
    <div className="h-screen flex flex-col bg-secondary-50">
      {/* Header */}
      <div className="bg-white border-b border-secondary-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-100 rounded-lg">
              <CalendarIcon className="h-6 w-6 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">Calendar</h1>
              <p className="text-secondary-600">
                {filteredTasks.length} scheduled task{filteredTasks.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Project Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-secondary-500" />
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="input min-w-[150px]"
              >
                <option value="all">All Projects</option>
                {projects.map(project => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
            </div>

            <Button onClick={() => handleTaskCreate()}>
              <Plus className="h-4 w-4 mr-2" />
              New Task
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex min-h-0">
        {/* Sidebar with Project Stats */}
        <div className="w-80 bg-white border-r border-secondary-200 p-6 overflow-y-auto">
          <div className="space-y-6">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-secondary-900">Overview</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Total Scheduled</span>
                    <span className="font-medium">{scheduledTasks.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Completed</span>
                    <span className="font-medium text-success-600">
                      {scheduledTasks.filter(t => t.status === 'DONE').length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">In Progress</span>
                    <span className="font-medium text-primary-600">
                      {scheduledTasks.filter(t => t.status === 'IN_PROGRESS').length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Overdue</span>
                    <span className="font-medium text-error-600">
                      {scheduledTasks.filter(t =>
                        t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'DONE'
                      ).length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Project Breakdown */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-secondary-900">Projects</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {getProjectStats().map(({ project, taskCount, completedCount }) => (
                    <div key={project.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: project.color || '#64748b' }}
                        />
                        <span className="text-sm font-medium text-secondary-900">
                          {project.name}
                        </span>
                      </div>
                      <div className="text-sm text-secondary-600">
                        {completedCount}/{taskCount}
                      </div>
                    </div>
                  ))}

                  {/* Unassigned tasks */}
                  {scheduledTasks.filter(t => !t.project).length > 0 && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 rounded-full bg-secondary-400" />
                        <span className="text-sm font-medium text-secondary-900">
                          No Project
                        </span>
                      </div>
                      <div className="text-sm text-secondary-600">
                        {scheduledTasks.filter(t => !t.project && t.status === 'DONE').length}/
                        {scheduledTasks.filter(t => !t.project).length}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Calendar View */}
        <div className="flex-1 p-6">
          <CalendarView
            tasks={filteredTasks}
            onTaskCreate={handleTaskCreate}
            onTaskClick={handleTaskClick}
          />
        </div>
      </div>

      {/* Create Task Modal */}
      <CreateTaskModal
        isOpen={showCreateTaskModal}
        onClose={() => {
          setShowCreateTaskModal(false);
          setSelectedDate(null);
        }}
        projects={projects}
        defaultDueDate={selectedDate}
        onSuccess={handleTaskCreated}
      />

      <QuickAdd />
    </div>
  );
};

export default CalendarPage;
