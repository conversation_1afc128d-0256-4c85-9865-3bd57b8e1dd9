import React, { useState, useEffect } from 'react';
import { Play, Pause, CheckCircle2, Clock, X } from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import TagBadge from '@/components/Tags/TagBadge';
import Button from '@/components/ui/Button';

interface CurrentTaskFooterProps {
  className?: string;
}

const CurrentTaskFooter: React.FC<CurrentTaskFooterProps> = ({ className = '' }) => {
  const { tasks, updateTask } = useTaskStore();
  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Find current task (IN_PROGRESS and scheduled for current time)
  useEffect(() => {
    const now = new Date();
    const currentHour = now.getHours();
    
    const activeTask = tasks.find(task => {
      if (task.status !== 'IN_PROGRESS' || !task.scheduledTime) return false;
      
      const scheduledTime = new Date(task.scheduledTime);
      const scheduledHour = scheduledTime.getHours();
      const scheduledDate = scheduledTime.toDateString();
      const todayDate = now.toDateString();
      
      // Task is active if it's scheduled for current hour and today
      return scheduledDate === todayDate && scheduledHour === currentHour;
    });

    setCurrentTask(activeTask || null);
    setIsVisible(!!activeTask);
    
    if (activeTask) {
      // Calculate time elapsed since scheduled time
      const scheduledTime = new Date(activeTask.scheduledTime!);
      const elapsed = Math.max(0, Math.floor((now.getTime() - scheduledTime.getTime()) / 1000));
      setTimeElapsed(elapsed);
    } else {
      setTimeElapsed(0);
    }
  }, [tasks]);

  // Update timer every second
  useEffect(() => {
    if (!currentTask || !currentTask.scheduledTime) return;

    const interval = setInterval(() => {
      const now = new Date();
      const scheduledTime = new Date(currentTask.scheduledTime!);
      const elapsed = Math.max(0, Math.floor((now.getTime() - scheduledTime.getTime()) / 1000));
      setTimeElapsed(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [currentTask]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePauseTask = async () => {
    if (!currentTask) return;
    
    try {
      await updateTask(currentTask.id, { status: 'PAUSED' });
      setIsVisible(false);
    } catch (error) {
      console.error('Failed to pause task:', error);
    }
  };

  const handleCompleteTask = async () => {
    if (!currentTask) return;
    
    try {
      await updateTask(currentTask.id, { status: 'DONE' });
      setIsVisible(false);
    } catch (error) {
      console.error('Failed to complete task:', error);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  if (!isVisible || !currentTask) {
    return null;
  }

  return (
    <div className={`fixed bottom-0 left-0 right-0 z-40 ${className}`}>
      <div className="bg-white border-t border-secondary-200 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Task Info */}
            <div className="flex items-center gap-4 flex-1 min-w-0">
              {/* Status Indicator */}
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-secondary-700">Currently Working</span>
              </div>

              {/* Task Details */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="text-sm font-semibold text-secondary-900 truncate">
                    {currentTask.title}
                  </h3>
                  {currentTask.tag && (
                    <TagBadge tag={currentTask.tag} size="sm" />
                  )}
                </div>
                
                <div className="flex items-center gap-3 text-xs text-secondary-600">
                  {currentTask.project && (
                    <div className="flex items-center gap-1">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: currentTask.project.color || '#64748b' }}
                      />
                      <span>{currentTask.project.name}</span>
                    </div>
                  )}
                  
                  {currentTask.scheduledTime && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>
                        Started at {new Date(currentTask.scheduledTime).toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: '2-digit',
                        })}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Timer */}
              <div className="text-right">
                <div className="text-lg font-mono font-bold text-blue-600">
                  {formatTime(timeElapsed)}
                </div>
                <div className="text-xs text-secondary-500">elapsed</div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2 ml-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePauseTask}
                className="flex items-center gap-1"
              >
                <Pause className="h-4 w-4" />
                Pause
              </Button>
              
              <Button
                variant="default"
                size="sm"
                onClick={handleCompleteTask}
                className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
              >
                <CheckCircle2 className="h-4 w-4" />
                Complete
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="ml-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CurrentTaskFooter;
