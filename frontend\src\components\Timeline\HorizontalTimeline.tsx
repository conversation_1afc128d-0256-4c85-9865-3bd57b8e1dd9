import React, { useState, useMemo } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
  useDraggable,
} from '@dnd-kit/core';
import {
  Clock,
  Filter,
  ZoomIn,
  ZoomOut,
  ChevronLeft,
  ChevronRight,
  Pause,
  CheckCircle2,
  Archive,
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import Button from '@/components/ui/Button';
import TagBadge from '@/components/Tags/TagBadge';
import CollapsibleTaskSection from './CollapsibleTaskSection';

interface TimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

type ViewMode = 'hourly' | 'daily';

// Generate time slots for hourly view (24 hours)
const generateHourlySlots = (date: Date) => {
  const slots = [];
  for (let hour = 0; hour < 24; hour++) {
    const slotDate = new Date(date);
    slotDate.setHours(hour, 0, 0, 0);
    slots.push({
      id: `${date.toDateString()}-${hour.toString().padStart(2, '0')}:00`,
      time: `${hour.toString().padStart(2, '0')}:00`,
      label: hour === 0 ? '12:00 AM' : hour < 12 ? `${hour}:00 AM` : hour === 12 ? '12:00 PM' : `${hour - 12}:00 PM`,
      date: slotDate,
      hour,
    });
  }
  return slots;
};

// Generate daily slots for weekly view
const generateDailySlots = (startDate: Date, days: number = 7) => {
  const slots = [];
  for (let i = 0; i < days; i++) {
    const slotDate = new Date(startDate);
    slotDate.setDate(startDate.getDate() + i);
    slotDate.setHours(0, 0, 0, 0);
    slots.push({
      id: slotDate.toDateString(),
      date: slotDate,
      label: slotDate.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
      isToday: slotDate.toDateString() === new Date().toDateString(),
    });
  }
  return slots;
};

// Draggable Timeline Task Component
const TimelineTask: React.FC<{
  task: Task;
  viewMode: ViewMode;
}> = ({ task, viewMode }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  const getStatusColor = () => {
    switch (task.status) {
      case 'IN_PROGRESS': return 'bg-blue-50 border-blue-200 text-blue-900';
      case 'DONE': return 'bg-green-50 border-green-200 text-green-900';
      case 'PAUSED': return 'bg-yellow-50 border-yellow-200 text-yellow-900';
      case 'ARCHIVED': return 'bg-gray-50 border-gray-200 text-gray-700';
      case 'TODO':
        // Distinguish between scheduled and unscheduled TODO tasks
        return task.scheduledTime
          ? 'bg-purple-50 border-purple-200 text-purple-900' // Scheduled
          : 'bg-white border-secondary-200 text-secondary-800'; // Unscheduled
      default: return 'bg-white border-secondary-200 text-secondary-800';
    }
  };

  const getStatusIndicatorColor = () => {
    // For scheduled TODO tasks, use project color if available
    if (task.status === 'TODO' && task.scheduledTime && task.project?.color) {
      return `bg-[${task.project.color}]`;
    }

    switch (task.status) {
      case 'IN_PROGRESS': return 'bg-blue-500';
      case 'DONE': return 'bg-green-500';
      case 'PAUSED': return 'bg-yellow-500';
      case 'ARCHIVED': return 'bg-gray-500';
      case 'TODO':
        // Distinguish between scheduled and unscheduled TODO tasks
        return task.scheduledTime
          ? 'bg-purple-500' // Scheduled (fallback if no project color)
          : 'bg-secondary-400'; // Unscheduled
      default: return 'bg-secondary-400';
    }
  };

  const getStatusTitle = () => {
    switch (task.status) {
      case 'IN_PROGRESS': return 'In Progress';
      case 'DONE': return 'Completed';
      case 'PAUSED': return 'Paused';
      case 'ARCHIVED': return 'Archived';
      case 'TODO':
        return task.scheduledTime ? 'Scheduled' : 'Unscheduled';
      default: return 'To Do';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        transition-all select-none
        ${isDragging ? 'opacity-50 rotate-1 scale-105 z-50' : ''}
      `}
    >
      <div
        {...attributes}
        {...listeners}
        className={`
          relative p-2 rounded-lg border-2 cursor-grab active:cursor-grabbing
          hover:shadow-md transition-all group select-none draggable
          ${getStatusColor()}
          ${viewMode === 'hourly' ? 'min-w-[120px] max-w-[200px]' : 'min-w-[100px] max-w-[150px]'}
        `}
      >
        {/* Task Content */}
        <div className="relative z-10 pointer-events-none select-none">
          <div className="flex items-center space-x-2 mb-1">
            {task.project && (
              <div
                className="w-2 h-2 rounded-full flex-shrink-0"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}
            <span className="text-xs font-medium truncate select-none">
              {task.title}
            </span>
          </div>

          {task.tag && (
            <div className="mt-1 select-none">
              <TagBadge tag={task.tag} size="sm" />
            </div>
          )}
        </div>

        {/* Status Indicator */}
        <div className="absolute top-1 right-1">
          <div
            className={`w-2 h-2 rounded-full ${
              task.status === 'TODO' && task.scheduledTime && task.project?.color
                ? ''
                : getStatusIndicatorColor()
            }`}
            style={
              task.status === 'TODO' && task.scheduledTime && task.project?.color
                ? { backgroundColor: task.project.color }
                : {}
            }
            title={getStatusTitle()}
          />
        </div>

        {/* Resize Handles */}
        <div
          className="absolute left-0 top-0 bottom-0 w-1 bg-transparent hover:bg-primary-400 cursor-ew-resize opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto"
          onMouseDown={(e) => e.stopPropagation()}
        />
        <div
          className="absolute right-0 top-0 bottom-0 w-1 bg-transparent hover:bg-primary-400 cursor-ew-resize opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto"
          onMouseDown={(e) => e.stopPropagation()}
        />
      </div>
    </div>
  );
};

// Timeline Slot Drop Zone
const TimelineSlot: React.FC<{
  slot: any;
  tasks: Task[];
  viewMode: ViewMode;
}> = ({ slot, tasks, viewMode }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: slot.id,
  });

  const isCurrentTime = () => {
    if (viewMode === 'hourly') {
      const now = new Date();
      return now.getHours() === slot.hour && slot.date.toDateString() === now.toDateString();
    } else {
      return slot.isToday;
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={`
        relative border-r border-secondary-200 transition-all
        ${viewMode === 'hourly' ? 'min-w-[150px] h-24' : 'min-w-[120px] h-32'}
        ${isOver ? 'bg-primary-50 border-primary-300' : 'hover:bg-secondary-50'}
        ${isCurrentTime() ? 'bg-blue-50 border-blue-300' : ''}
      `}
    >
      {/* Time Label */}
      <div className="absolute top-1 left-2 text-xs font-medium text-secondary-600">
        {slot.label}
      </div>

      {/* Current Time Indicator */}
      {isCurrentTime() && (
        <div className="absolute top-0 left-0 w-full h-1 bg-blue-500" />
      )}

      {/* Tasks */}
      <div className="pt-6 px-2 space-y-1 h-full overflow-hidden">
        {tasks.map((task) => (
          <TimelineTask
            key={task.id}
            task={task}
            viewMode={viewMode}
          />
        ))}
      </div>

      {/* Drop Indicator */}
      {isOver && (
        <div className="absolute inset-2 border-2 border-dashed border-primary-400 rounded bg-primary-50 flex items-center justify-center">
          <span className="text-xs text-primary-600 font-medium">Drop here</span>
        </div>
      )}
    </div>
  );
};

const HorizontalTimeline: React.FC<TimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();
  const [viewMode, setViewMode] = useState<ViewMode>('hourly');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);

  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Generate time slots based on view mode
  const timeSlots = useMemo(() => {
    if (viewMode === 'hourly') {
      return generateHourlySlots(currentDate);
    } else {
      const startOfWeek = new Date(currentDate);
      startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
      return generateDailySlots(startOfWeek, 7);
    }
  }, [viewMode, currentDate]);

  // Filter tasks
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (selectedProject !== 'all' && task.projectId !== selectedProject) {
        return false;
      }
      if (selectedTag !== 'all' && task.tagId !== selectedTag) {
        return false;
      }
      return true;
    });
  }, [tasks, selectedProject, selectedTag]);

  // Get tasks for specific slot (only show scheduled and in-progress tasks)
  const getTasksForSlot = (slot: any) => {
    return filteredTasks.filter(task => {
      if (!task.scheduledTime) return false;

      // Only show tasks that are scheduled or in progress on the timeline
      if (!['TODO', 'IN_PROGRESS'].includes(task.status)) return false;

      const taskDate = new Date(task.scheduledTime);

      if (viewMode === 'hourly') {
        return taskDate.getHours() === slot.hour &&
               taskDate.toDateString() === slot.date.toDateString();
      } else {
        return taskDate.toDateString() === slot.date.toDateString();
      }
    });
  };

  // Categorize tasks by status
  const unscheduledTasks = filteredTasks.filter(task =>
    !task.scheduledTime && task.status === 'TODO'
  );

  const pausedTasks = filteredTasks.filter(task =>
    task.status === 'PAUSED'
  );

  const completedTasks = filteredTasks.filter(task =>
    task.status === 'DONE'
  );

  const archivedTasks = filteredTasks.filter(task =>
    task.status === 'ARCHIVED'
  );

  const handleDragStart = (event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const dropZoneId = over.id as string;

    try {
      // Handle section drops - remove from timeline and update status
      if (dropZoneId === 'unscheduled-section') {
        await updateTask(taskId, {
          status: 'TODO',
          scheduledTime: '', // Remove from timeline (empty string to clear)
        });
        return;
      }

      if (dropZoneId === 'paused-section') {
        await updateTask(taskId, {
          status: 'PAUSED',
          scheduledTime: undefined, // Remove from timeline
        });
        return;
      }

      if (dropZoneId === 'completed-section') {
        await updateTask(taskId, {
          status: 'DONE',
          scheduledTime: undefined, // Remove from timeline
        });
        return;
      }

      if (dropZoneId === 'archived-section') {
        await updateTask(taskId, {
          status: 'ARCHIVED',
          scheduledTime: undefined, // Remove from timeline
        });
        return;
      }

      // Handle timeline slot drops
      let scheduledTime: Date;

      if (viewMode === 'hourly') {
        const [dateStr, timeStr] = dropZoneId.split('-');
        const [hours] = timeStr.split(':');
        scheduledTime = new Date(dateStr);
        scheduledTime.setHours(parseInt(hours), 0, 0, 0);
      } else {
        scheduledTime = new Date(dropZoneId);
        scheduledTime.setHours(9, 0, 0, 0); // Default to 9 AM for daily view
      }

      await updateTask(taskId, {
        status: 'TODO', // Set as scheduled TODO task
        scheduledTime: scheduledTime.toISOString(),
      });
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };





  const navigateTime = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (viewMode === 'hourly') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    onDateChange(newDate);
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col dnd-context">
        {/* Timeline Header */}
        <div className="flex-shrink-0 p-4 border-b border-secondary-200 bg-white">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-secondary-900 flex items-center">
              <Clock className="h-5 w-5 mr-2 text-primary-600" />
              Timeline View
            </h2>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'hourly' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('hourly')}
              >
                <ZoomIn className="h-4 w-4 mr-1" />
                Hourly
              </Button>
              <Button
                variant={viewMode === 'daily' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('daily')}
              >
                <ZoomOut className="h-4 w-4 mr-1" />
                Daily
              </Button>
            </div>
          </div>

          {/* Navigation and Filters */}
          <div className="flex items-center justify-between">
            {/* Date Navigation */}
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => navigateTime('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm font-medium text-secondary-700 min-w-[200px] text-center">
                {viewMode === 'hourly'
                  ? currentDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })
                  : `Week of ${timeSlots[0]?.label} - ${timeSlots[6]?.label}`
                }
              </span>
              <Button variant="ghost" size="sm" onClick={() => navigateTime('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              {/* Project Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-secondary-600" />
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="text-sm border border-secondary-300 rounded px-2 py-1"
                >
                  <option value="all">All Projects</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Tag Filter */}
              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="text-sm border border-secondary-300 rounded px-2 py-1"
              >
                <option value="all">All Tags</option>
                {tags.map((tag) => (
                  <option key={tag.id} value={tag.id}>
                    {tag.name}
                  </option>
                ))}
              </select>


            </div>
          </div>
        </div>

        {/* Timeline Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Task Sections Sidebar */}
          <div className="w-80 flex-shrink-0 border-r border-secondary-200 bg-secondary-50 p-4 overflow-y-auto">
            {/* Unscheduled Tasks */}
            <CollapsibleTaskSection
              id="unscheduled-section"
              title="Unscheduled Tasks"
              icon={<Clock className="h-4 w-4" />}
              tasks={unscheduledTasks}
              color="blue"
              description="Tasks waiting to be scheduled"
              defaultExpanded={true}
            />

            {/* Paused Tasks */}
            <CollapsibleTaskSection
              id="paused-section"
              title="Paused Tasks"
              icon={<Pause className="h-4 w-4" />}
              tasks={pausedTasks}
              color="yellow"
              description="Tasks temporarily paused"
              defaultExpanded={false}
            />

            {/* Completed Tasks */}
            <CollapsibleTaskSection
              id="completed-section"
              title="Completed Tasks"
              icon={<CheckCircle2 className="h-4 w-4" />}
              tasks={completedTasks}
              color="green"
              description="Finished tasks"
              defaultExpanded={false}
            />

            {/* Archived Tasks */}
            <CollapsibleTaskSection
              id="archived-section"
              title="Archived Tasks"
              icon={<Archive className="h-4 w-4" />}
              tasks={archivedTasks}
              color="gray"
              description="Archived tasks"
              defaultExpanded={false}
            />
          </div>

          {/* Timeline Grid */}
          <div className="flex-1 overflow-x-auto overflow-y-hidden">
            <div className="flex h-full min-w-max">
              {timeSlots.map((slot) => (
                <TimelineSlot
                  key={slot.id}
                  slot={slot}
                  tasks={getTasksForSlot(slot)}
                  viewMode={viewMode}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Drag Overlay */}
        <DragOverlay>
          {draggedTask ? (
            <TimelineTask
              task={draggedTask}
              viewMode={viewMode}
            />
          ) : null}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

export default HorizontalTimeline;
