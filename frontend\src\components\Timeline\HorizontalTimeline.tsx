import React, { useState, useMemo } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
  useDraggable,
} from '@dnd-kit/core';
import {
  Clock,
  Filter,
  ZoomIn,
  ZoomOut,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  CheckCircle2,
  RotateCcw,
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import Button from '@/components/ui/Button';
import TagBadge from '@/components/Tags/TagBadge';
import TaskZones from './TaskZones';

interface TimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

type ViewMode = 'hourly' | 'daily';
type TaskStatus = 'scheduled' | 'in_progress' | 'completed' | 'paused';

// Generate time slots for hourly view (24 hours)
const generateHourlySlots = (date: Date) => {
  const slots = [];
  for (let hour = 0; hour < 24; hour++) {
    const slotDate = new Date(date);
    slotDate.setHours(hour, 0, 0, 0);
    slots.push({
      id: `${date.toDateString()}-${hour.toString().padStart(2, '0')}:00`,
      time: `${hour.toString().padStart(2, '0')}:00`,
      label: hour === 0 ? '12:00 AM' : hour < 12 ? `${hour}:00 AM` : hour === 12 ? '12:00 PM' : `${hour - 12}:00 PM`,
      date: slotDate,
      hour,
    });
  }
  return slots;
};

// Generate daily slots for weekly view
const generateDailySlots = (startDate: Date, days: number = 7) => {
  const slots = [];
  for (let i = 0; i < days; i++) {
    const slotDate = new Date(startDate);
    slotDate.setDate(startDate.getDate() + i);
    slotDate.setHours(0, 0, 0, 0);
    slots.push({
      id: slotDate.toDateString(),
      date: slotDate,
      label: slotDate.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
      isToday: slotDate.toDateString() === new Date().toDateString(),
    });
  }
  return slots;
};

// Draggable Timeline Task Component
const TimelineTask: React.FC<{
  task: Task;
  viewMode: ViewMode;
  onStatusChange: (taskId: string, status: TaskStatus) => void;
}> = ({ task, viewMode, onStatusChange }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  const getStatusColor = () => {
    switch (task.status) {
      case 'IN_PROGRESS': return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'DONE': return 'bg-green-100 border-green-300 text-green-800';
      default: return 'bg-white border-secondary-300 text-secondary-800';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`transition-all ${isDragging ? 'opacity-50 rotate-1 scale-105 z-50' : ''}`}
    >
      <div className={`
        relative p-2 rounded-lg border-2 cursor-grab active:cursor-grabbing
        hover:shadow-md transition-all group
        ${getStatusColor()}
        ${viewMode === 'hourly' ? 'min-w-[120px] max-w-[200px]' : 'min-w-[100px] max-w-[150px]'}
      `}>
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="absolute inset-0 cursor-grab active:cursor-grabbing"
        />

        {/* Task Content */}
        <div className="relative z-10 pointer-events-none">
          <div className="flex items-center space-x-2 mb-1">
            {task.project && (
              <div
                className="w-2 h-2 rounded-full flex-shrink-0"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}
            <span className="text-xs font-medium truncate">
              {task.title}
            </span>
          </div>

          {task.tag && (
            <div className="mt-1">
              <TagBadge tag={task.tag} size="sm" />
            </div>
          )}
        </div>

        {/* Status Action Buttons */}
        <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto">
          <div className="flex space-x-1">
            {task.status !== 'IN_PROGRESS' && (
              <button
                onClick={() => onStatusChange(task.id, 'in_progress')}
                className="p-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                title="Start Task"
              >
                <Play className="h-3 w-3" />
              </button>
            )}
            {task.status === 'IN_PROGRESS' && (
              <button
                onClick={() => onStatusChange(task.id, 'paused')}
                className="p-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
                title="Pause Task"
              >
                <Pause className="h-3 w-3" />
              </button>
            )}
            {task.status !== 'DONE' && (
              <button
                onClick={() => onStatusChange(task.id, 'completed')}
                className="p-1 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                title="Complete Task"
              >
                <CheckCircle2 className="h-3 w-3" />
              </button>
            )}
            {task.status === 'DONE' && (
              <button
                onClick={() => onStatusChange(task.id, 'scheduled')}
                className="p-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                title="Reopen Task"
              >
                <RotateCcw className="h-3 w-3" />
              </button>
            )}
          </div>
        </div>

        {/* Resize Handles */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-transparent hover:bg-primary-400 cursor-ew-resize opacity-0 group-hover:opacity-100 transition-opacity" />
        <div className="absolute right-0 top-0 bottom-0 w-1 bg-transparent hover:bg-primary-400 cursor-ew-resize opacity-0 group-hover:opacity-100 transition-opacity" />
      </div>
    </div>
  );
};

// Timeline Slot Drop Zone
const TimelineSlot: React.FC<{
  slot: any;
  tasks: Task[];
  viewMode: ViewMode;
  onStatusChange: (taskId: string, status: TaskStatus) => void;
}> = ({ slot, tasks, viewMode, onStatusChange }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: slot.id,
  });

  const isCurrentTime = () => {
    if (viewMode === 'hourly') {
      const now = new Date();
      return now.getHours() === slot.hour && slot.date.toDateString() === now.toDateString();
    } else {
      return slot.isToday;
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={`
        relative border-r border-secondary-200 transition-all
        ${viewMode === 'hourly' ? 'min-w-[150px] h-24' : 'min-w-[120px] h-32'}
        ${isOver ? 'bg-primary-50 border-primary-300' : 'hover:bg-secondary-50'}
        ${isCurrentTime() ? 'bg-blue-50 border-blue-300' : ''}
      `}
    >
      {/* Time Label */}
      <div className="absolute top-1 left-2 text-xs font-medium text-secondary-600">
        {slot.label}
      </div>

      {/* Current Time Indicator */}
      {isCurrentTime() && (
        <div className="absolute top-0 left-0 w-full h-1 bg-blue-500" />
      )}

      {/* Tasks */}
      <div className="pt-6 px-2 space-y-1 h-full overflow-hidden">
        {tasks.map((task) => (
          <TimelineTask
            key={task.id}
            task={task}
            viewMode={viewMode}
            onStatusChange={onStatusChange}
          />
        ))}
      </div>

      {/* Drop Indicator */}
      {isOver && (
        <div className="absolute inset-2 border-2 border-dashed border-primary-400 rounded bg-primary-50 flex items-center justify-center">
          <span className="text-xs text-primary-600 font-medium">Drop here</span>
        </div>
      )}
    </div>
  );
};

const HorizontalTimeline: React.FC<TimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();
  const [viewMode, setViewMode] = useState<ViewMode>('hourly');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [showZones, setShowZones] = useState(false);
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);

  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Generate time slots based on view mode
  const timeSlots = useMemo(() => {
    if (viewMode === 'hourly') {
      return generateHourlySlots(currentDate);
    } else {
      const startOfWeek = new Date(currentDate);
      startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
      return generateDailySlots(startOfWeek, 7);
    }
  }, [viewMode, currentDate]);

  // Filter tasks
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (selectedProject !== 'all' && task.projectId !== selectedProject) {
        return false;
      }
      if (selectedTag !== 'all' && task.tagId !== selectedTag) {
        return false;
      }
      return true;
    });
  }, [tasks, selectedProject, selectedTag]);

  // Get tasks for specific slot
  const getTasksForSlot = (slot: any) => {
    return filteredTasks.filter(task => {
      if (!task.scheduledTime) return false;

      const taskDate = new Date(task.scheduledTime);

      if (viewMode === 'hourly') {
        return taskDate.getHours() === slot.hour &&
               taskDate.toDateString() === slot.date.toDateString();
      } else {
        return taskDate.toDateString() === slot.date.toDateString();
      }
    });
  };

  // Get unscheduled tasks
  const unscheduledTasks = filteredTasks.filter(task =>
    !task.scheduledTime && task.status !== 'DONE'
  );

  const handleDragStart = (event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const dropZoneId = over.id as string;

    try {
      // Handle zone drops
      if (dropZoneId.startsWith('not-scheduled')) {
        await updateTask(taskId, {
          status: 'TODO',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'paused') {
        await updateTask(taskId, {
          status: 'PAUSED',
        });
        return;
      }

      if (dropZoneId === 'completed') {
        await updateTask(taskId, {
          status: 'DONE',
        });
        return;
      }

      if (dropZoneId === 'archived') {
        await updateTask(taskId, {
          status: 'ARCHIVED',
        });
        return;
      }

      // Handle timeline slot drops
      let scheduledTime: Date;

      if (viewMode === 'hourly') {
        const [dateStr, timeStr] = dropZoneId.split('-');
        const [hours] = timeStr.split(':');
        scheduledTime = new Date(dateStr);
        scheduledTime.setHours(parseInt(hours), 0, 0, 0);
      } else {
        scheduledTime = new Date(dropZoneId);
        scheduledTime.setHours(9, 0, 0, 0); // Default to 9 AM for daily view
      }

      await updateTask(taskId, {
        status: 'IN_PROGRESS',
        scheduledTime: scheduledTime.toISOString(),
      });
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };

  const handleStatusChange = async (taskId: string, status: TaskStatus) => {
    try {
      let updateData: any = {};

      switch (status) {
        case 'in_progress':
          updateData.status = 'IN_PROGRESS';
          break;
        case 'completed':
          updateData.status = 'DONE';
          break;
        case 'paused':
        case 'scheduled':
          updateData.status = 'TODO';
          if (status === 'paused') {
            updateData.scheduledTime = null; // Remove from schedule
          }
          break;
      }

      await updateTask(taskId, updateData);
    } catch (error) {
      console.error('Failed to update task status:', error);
    }
  };

  const navigateTime = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (viewMode === 'hourly') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    onDateChange(newDate);
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col">
        {/* Timeline Header */}
        <div className="flex-shrink-0 p-4 border-b border-secondary-200 bg-white">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-secondary-900 flex items-center">
              <Clock className="h-5 w-5 mr-2 text-primary-600" />
              Timeline View
            </h2>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'hourly' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('hourly')}
              >
                <ZoomIn className="h-4 w-4 mr-1" />
                Hourly
              </Button>
              <Button
                variant={viewMode === 'daily' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('daily')}
              >
                <ZoomOut className="h-4 w-4 mr-1" />
                Daily
              </Button>
            </div>
          </div>

          {/* Navigation and Filters */}
          <div className="flex items-center justify-between">
            {/* Date Navigation */}
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => navigateTime('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm font-medium text-secondary-700 min-w-[200px] text-center">
                {viewMode === 'hourly'
                  ? currentDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })
                  : `Week of ${timeSlots[0]?.label} - ${timeSlots[6]?.label}`
                }
              </span>
              <Button variant="ghost" size="sm" onClick={() => navigateTime('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              {/* Project Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-secondary-600" />
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="text-sm border border-secondary-300 rounded px-2 py-1"
                >
                  <option value="all">All Projects</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Tag Filter */}
              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="text-sm border border-secondary-300 rounded px-2 py-1"
              >
                <option value="all">All Tags</option>
                {tags.map((tag) => (
                  <option key={tag.id} value={tag.id}>
                    {tag.name}
                  </option>
                ))}
              </select>

              {/* Toggle Zones */}
              <Button
                variant={showZones ? 'default' : 'outline'}
                size="sm"
                onClick={() => setShowZones(!showZones)}
              >
                Zones
              </Button>
            </div>
          </div>
        </div>

        {/* Task Zones (when enabled) */}
        {showZones && (
          <div className="border-t border-secondary-200 p-4">
            <TaskZones tasks={filteredTasks} />
          </div>
        )}

        {/* Timeline Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Unscheduled Tasks Sidebar */}
          <div className="w-64 flex-shrink-0 border-r border-secondary-200 bg-secondary-50 p-4 overflow-y-auto">
            <h3 className="text-sm font-semibold text-secondary-900 mb-3">
              Unscheduled Tasks ({unscheduledTasks.length})
            </h3>
            <div className="space-y-2">
              {unscheduledTasks.map((task) => (
                <TimelineTask
                  key={task.id}
                  task={task}
                  viewMode={viewMode}
                  onStatusChange={handleStatusChange}
                />
              ))}
            </div>
          </div>

          {/* Timeline Grid */}
          <div className="flex-1 overflow-x-auto overflow-y-hidden">
            <div className="flex h-full min-w-max">
              {timeSlots.map((slot) => (
                <TimelineSlot
                  key={slot.id}
                  slot={slot}
                  tasks={getTasksForSlot(slot)}
                  viewMode={viewMode}
                  onStatusChange={handleStatusChange}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Drag Overlay */}
        <DragOverlay>
          {draggedTask ? (
            <TimelineTask
              task={draggedTask}
              viewMode={viewMode}
              onStatusChange={handleStatusChange}
            />
          ) : null}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

export default HorizontalTimeline;
