import React, { useState, useEffect } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { MoreHorizontal, Plus, GripVertical, Settings } from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import ColumnManager, { BoardColumn } from '@/components/ColumnManager';

interface KanbanBoardProps {
  tasks: Task[];
  projectId?: string;
  onTaskCreate?: () => void;
}

// Default columns
const DEFAULT_COLUMNS: BoardColumn[] = [
  { id: 'TODO', name: 'To Do', color: 'bg-secondary-100', order: 0, isDefault: true },
  { id: 'IN_PROGRESS', name: 'In Progress', color: 'bg-primary-100', order: 1, isDefault: true },
  { id: 'DONE', name: 'Done', color: 'bg-success-100', order: 2, isDefault: true },
  { id: 'ARCHIVED', name: 'Archived', color: 'bg-secondary-50', order: 3, isDefault: true },
];

// Sortable Task Item Component
interface SortableTaskItemProps {
  task: Task;
}

const SortableTaskItem: React.FC<SortableTaskItemProps> = ({ task }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };



  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`transition-all ${isDragging ? 'opacity-50 rotate-2 scale-105' : ''}`}
    >
      <Card className="hover:shadow-soft-md transition-shadow cursor-grab active:cursor-grabbing">
        <CardContent className="p-4">
          <div className="space-y-2">
            {/* Drag Handle and Title */}
            <div className="flex items-start space-x-2">
              <div
                {...attributes}
                {...listeners}
                className="mt-1 text-secondary-400 hover:text-secondary-600 cursor-grab active:cursor-grabbing"
              >
                <GripVertical className="h-4 w-4" />
              </div>
              <h4 className="font-medium text-secondary-900 line-clamp-2 flex-1">
                {task.title}
              </h4>
            </div>

            {task.content && (
              <p className="text-sm text-secondary-600 line-clamp-3 ml-6">
                {task.content}
              </p>
            )}

            <div className="flex items-center justify-between text-xs ml-6">
              {task.priority && (
                <span className={`px-2 py-1 rounded-full font-medium ${
                  task.priority === 'URGENT' ? 'bg-error-100 text-error-700' :
                  task.priority === 'HIGH' ? 'bg-warning-100 text-warning-700' :
                  task.priority === 'MEDIUM' ? 'bg-primary-100 text-primary-700' :
                  'bg-secondary-100 text-secondary-700'
                }`}>
                  {task.priority.toLowerCase()}
                </span>
              )}

              {task.dueDate && (
                <span className="text-secondary-500">
                  {new Date(task.dueDate).toLocaleDateString()}
                </span>
              )}
            </div>

            {task.project && (
              <div className="flex items-center space-x-2 ml-6">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: task.project.color || '#64748b' }}
                />
                <span className="text-xs text-secondary-600">
                  {task.project.name}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Droppable Column Component
interface DroppableColumnProps {
  column: BoardColumn;
  tasks: Task[];
  onTaskCreate?: () => void;
}

const DroppableColumn: React.FC<DroppableColumnProps> = ({ column, tasks, onTaskCreate }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: column.id,
  });

  return (
    <div className="flex flex-col h-full">
      {/* Column Header */}
      <div className={`p-4 rounded-t-lg ${column.color} border-b`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-secondary-900">
              {column.name}
            </h3>
            <span className="text-sm text-secondary-600 bg-white px-2 py-1 rounded-full">
              {tasks.length}
            </span>
          </div>
          <Button variant="ghost" size="sm" className="p-1">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Column Content - Droppable Area */}
      <div
        ref={setNodeRef}
        className={`flex-1 p-4 space-y-3 min-h-[200px] transition-colors ${
          isOver ? 'bg-primary-50' : 'bg-secondary-25'
        }`}
      >
        <SortableContext
          items={tasks.map(task => task.id)}
          strategy={verticalListSortingStrategy}
        >
          {tasks.map((task) => (
            <SortableTaskItem key={task.id} task={task} />
          ))}
        </SortableContext>

        {/* Add Task Button */}
        {column.id === 'TODO' && onTaskCreate && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onTaskCreate}
            className="w-full border-2 border-dashed border-secondary-300 hover:border-primary-400 hover:bg-primary-50"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Task
          </Button>
        )}
      </div>
    </div>
  );
};

const KanbanBoard: React.FC<KanbanBoardProps> = ({
  tasks,
  onTaskCreate,
}) => {
  const { updateTask } = useTaskStore();
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const [columns, setColumns] = useState<BoardColumn[]>(DEFAULT_COLUMNS);
  const [showColumnManager, setShowColumnManager] = useState(false);

  // Load columns from localStorage on mount
  useEffect(() => {
    const savedColumns = localStorage.getItem('kanban-columns');
    if (savedColumns) {
      try {
        const parsedColumns = JSON.parse(savedColumns);
        setColumns(parsedColumns);
      } catch (error) {
        console.error('Failed to parse saved columns:', error);
        setColumns(DEFAULT_COLUMNS);
      }
    }
  }, []);

  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Handle column changes
  const handleColumnsChange = (newColumns: BoardColumn[]) => {
    setColumns(newColumns);
    localStorage.setItem('kanban-columns', JSON.stringify(newColumns));
  };

  // Group tasks by status
  const tasksByStatus = columns.reduce((acc, column) => {
    acc[column.id] = tasks.filter(task => task.status === column.id);
    return acc;
  }, {} as Record<string, Task[]>);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = tasks.find(t => t.id === active.id);
    setActiveTask(task || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const newStatus = over.id as 'TODO' | 'IN_PROGRESS' | 'DONE' | 'ARCHIVED';

    // Find the task and check if status actually changed
    const task = tasks.find(t => t.id === taskId);
    if (!task || task.status === newStatus) return;

    try {
      await updateTask(taskId, { status: newStatus });
    } catch (error) {
      console.error('Failed to update task status:', error);
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full overflow-hidden">
        {/* Header with Column Management */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-secondary-900">
            Board View
          </h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowColumnManager(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Manage Columns
          </Button>
        </div>

        <div className={`grid gap-6 h-full`} style={{
          gridTemplateColumns: `repeat(${columns.length}, minmax(280px, 1fr))`
        }}>
          {columns
            .sort((a, b) => a.order - b.order)
            .map((column) => (
            <DroppableColumn
              key={column.id}
              column={column}
              tasks={tasksByStatus[column.id] || []}
              onTaskCreate={column.id === 'TODO' ? onTaskCreate : undefined}
            />
          ))}
        </div>
      </div>

      {/* Drag Overlay */}
      <DragOverlay>
        {activeTask ? (
          <div className="rotate-2 scale-105 opacity-90">
            <Card className="shadow-lg">
              <CardContent className="p-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-secondary-900 line-clamp-2">
                    {activeTask.title}
                  </h4>
                  {activeTask.content && (
                    <p className="text-sm text-secondary-600 line-clamp-3">
                      {activeTask.content}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : null}
      </DragOverlay>

      {/* Column Manager Modal */}
      <ColumnManager
        columns={columns}
        onColumnsChange={handleColumnsChange}
        isOpen={showColumnManager}
        onClose={() => setShowColumnManager(false)}
      />
    </DndContext>
  );
};

export default KanbanBoard;
