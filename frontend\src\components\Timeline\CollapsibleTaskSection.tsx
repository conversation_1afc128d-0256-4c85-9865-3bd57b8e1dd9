import React, { useState } from 'react';
import { useDroppable, useDraggable } from '@dnd-kit/core';
import { ChevronDown, ChevronUp, Play, Pause, CheckCircle2, RotateCcw } from 'lucide-react';
import { Task } from '@/services/api';
import TagBadge from '@/components/Tags/TagBadge';

type TaskStatus = 'scheduled' | 'in_progress' | 'completed' | 'paused';

// Draggable Timeline Task Component for Sections
const SectionTask: React.FC<{
  task: Task;
  onStatusChange: (taskId: string, status: TaskStatus) => void;
}> = ({ task, onStatusChange }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  const getStatusColor = () => {
    switch (task.status) {
      case 'IN_PROGRESS': return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'DONE': return 'bg-green-100 border-green-300 text-green-800';
      case 'PAUSED': return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case 'ARCHIVED': return 'bg-gray-100 border-gray-300 text-gray-800';
      default: return 'bg-white border-secondary-300 text-secondary-800';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`transition-all ${isDragging ? 'opacity-50 rotate-1 scale-105 z-50' : ''}`}
    >
      <div className={`
        relative p-3 rounded-lg border-2 cursor-grab active:cursor-grabbing
        hover:shadow-md transition-all group
        ${getStatusColor()}
      `}>
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="absolute inset-0 cursor-grab active:cursor-grabbing"
        />

        {/* Task Content */}
        <div className="relative z-10 pointer-events-none">
          <div className="flex items-center space-x-2 mb-1">
            {task.project && (
              <div
                className="w-2 h-2 rounded-full flex-shrink-0"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}
            <span className="text-sm font-medium truncate">
              {task.title}
            </span>
          </div>

          {task.content && (
            <p className="text-xs text-secondary-600 mb-2 line-clamp-2">
              {task.content}
            </p>
          )}

          {task.tag && (
            <div className="mb-2">
              <TagBadge tag={task.tag} size="sm" />
            </div>
          )}

          {task.dueDate && (
            <div className="text-xs text-secondary-500">
              Due: {new Date(task.dueDate).toLocaleDateString()}
            </div>
          )}
        </div>

        {/* Status Action Buttons */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto">
          <div className="flex space-x-1">
            {task.status !== 'IN_PROGRESS' && (
              <button
                onClick={() => onStatusChange(task.id, 'in_progress')}
                className="p-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                title="Start Task"
              >
                <Play className="h-3 w-3" />
              </button>
            )}
            {task.status === 'IN_PROGRESS' && (
              <button
                onClick={() => onStatusChange(task.id, 'paused')}
                className="p-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
                title="Pause Task"
              >
                <Pause className="h-3 w-3" />
              </button>
            )}
            {task.status !== 'DONE' && (
              <button
                onClick={() => onStatusChange(task.id, 'completed')}
                className="p-1 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                title="Complete Task"
              >
                <CheckCircle2 className="h-3 w-3" />
              </button>
            )}
            {task.status === 'DONE' && (
              <button
                onClick={() => onStatusChange(task.id, 'scheduled')}
                className="p-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                title="Reopen Task"
              >
                <RotateCcw className="h-3 w-3" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

interface CollapsibleTaskSectionProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  tasks: Task[];
  color: string;
  description: string;
  defaultExpanded?: boolean;
  onStatusChange: (taskId: string, status: TaskStatus) => void;
}

const CollapsibleTaskSection: React.FC<CollapsibleTaskSectionProps> = ({
  id,
  title,
  icon,
  tasks,
  color,
  description,
  defaultExpanded = true,
  onStatusChange,
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const { setNodeRef, isOver } = useDroppable({
    id,
  });

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        text: 'text-blue-700',
        icon: 'text-blue-600',
        hover: 'hover:bg-blue-100',
        drop: 'border-blue-400 bg-blue-100',
      },
      yellow: {
        bg: 'bg-yellow-50',
        border: 'border-yellow-200',
        text: 'text-yellow-700',
        icon: 'text-yellow-600',
        hover: 'hover:bg-yellow-100',
        drop: 'border-yellow-400 bg-yellow-100',
      },
      green: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        text: 'text-green-700',
        icon: 'text-green-600',
        hover: 'hover:bg-green-100',
        drop: 'border-green-400 bg-green-100',
      },
      gray: {
        bg: 'bg-gray-50',
        border: 'border-gray-200',
        text: 'text-gray-700',
        icon: 'text-gray-600',
        hover: 'hover:bg-gray-100',
        drop: 'border-gray-400 bg-gray-100',
      },
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.gray;
  };

  const colors = getColorClasses(color);

  return (
    <div className="mb-4">
      {/* Section Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-full flex items-center justify-between p-3 rounded-lg border-2 transition-all ${
          isOver ? colors.drop : `${colors.bg} ${colors.border} ${colors.hover}`
        }`}
      >
        <div className="flex items-center gap-3">
          <div className={`p-1.5 rounded-md ${colors.bg}`}>
            <div className={colors.icon}>
              {icon}
            </div>
          </div>
          <div className="text-left">
            <h3 className={`font-semibold ${colors.text}`}>{title}</h3>
            <p className="text-xs text-secondary-600">{description}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors.bg} ${colors.text}`}>
            {tasks.length}
          </span>
          {isExpanded ? (
            <ChevronUp className={`h-4 w-4 ${colors.icon}`} />
          ) : (
            <ChevronDown className={`h-4 w-4 ${colors.icon}`} />
          )}
        </div>
      </button>

      {/* Drop Zone for Collapsed State */}
      {!isExpanded && (
        <div
          ref={setNodeRef}
          className={`mt-2 p-2 border-2 border-dashed rounded-lg transition-all ${
            isOver ? colors.drop : 'border-transparent'
          }`}
        >
          {isOver && (
            <p className={`text-center text-sm font-medium ${colors.text}`}>
              Drop task here
            </p>
          )}
        </div>
      )}

      {/* Expanded Content */}
      {isExpanded && (
        <div
          ref={setNodeRef}
          className={`mt-2 space-y-2 p-3 rounded-lg border-2 transition-all min-h-[60px] ${
            isOver ? colors.drop : `${colors.bg} ${colors.border}`
          }`}
        >
          {/* Drop Indicator */}
          {isOver && tasks.length === 0 && (
            <div className={`p-4 border-2 border-dashed rounded-lg ${colors.border}`}>
              <p className={`text-center text-sm font-medium ${colors.text}`}>
                Drop task here
              </p>
            </div>
          )}

          {/* Tasks */}
          {tasks.length > 0 ? (
            tasks.map((task) => (
              <SectionTask
                key={task.id}
                task={task}
                onStatusChange={onStatusChange}
              />
            ))
          ) : !isOver ? (
            <div className="text-center py-4">
              <div className={`w-8 h-8 mx-auto mb-2 rounded-full ${colors.bg} flex items-center justify-center`}>
                <div className={colors.icon}>
                  {icon}
                </div>
              </div>
              <p className="text-sm text-secondary-500">No tasks in this section</p>
            </div>
          ) : null}

          {/* Drop indicator when tasks exist */}
          {isOver && tasks.length > 0 && (
            <div className={`p-2 border-2 border-dashed rounded-lg ${colors.border}`}>
              <p className={`text-center text-xs font-medium ${colors.text}`}>
                Drop task here
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CollapsibleTaskSection;
