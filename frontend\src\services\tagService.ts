import { api } from './api';

export interface Tag {
  id: string;
  name: string;
  icon: string;
  color: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  projectId: string;
  project?: {
    id: string;
    name: string;
    color: string;
  };
  _count?: {
    tasks: number;
  };
}

export interface CreateTagData {
  name: string;
  icon: string;
  color: string;
}

export interface UpdateTagData {
  name?: string;
  icon?: string;
  color?: string;
}

class TagService {
  /**
   * Get all tags for the current user
   */
  async getAllTags(): Promise<Tag[]> {
    const response = await api.get('/tags');
    return response.data.data.tags;
  }

  /**
   * Get all tags for a specific project
   */
  async getProjectTags(projectId: string): Promise<Tag[]> {
    const response = await api.get(`/projects/${projectId}/tags`);
    return response.data.data.tags;
  }

  /**
   * Create a new tag for a project
   */
  async createTag(projectId: string, tagData: CreateTagData): Promise<Tag> {
    const response = await api.post(`/projects/${projectId}/tags`, tagData);
    return response.data.data.tag;
  }

  /**
   * Update an existing tag
   */
  async updateTag(tagId: string, tagData: UpdateTagData): Promise<Tag> {
    const response = await api.put(`/tags/${tagId}`, tagData);
    return response.data.data.tag;
  }

  /**
   * Delete a tag
   */
  async deleteTag(tagId: string): Promise<void> {
    await api.delete(`/tags/${tagId}`);
  }
}

export const tagService = new TagService();
