import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import {
  createTaskSchema,
  updateTaskSchema,
  idParamSchema,
  paginationSchema
} from '../config/validation';

const prisma = new PrismaClient();

export class TaskController {
  /**
   * Create a new task
   */
  static async createTask(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Validate input
      const taskData = createTaskSchema.parse(req.body);

      // If projectId is provided, verify it belongs to the user
      if (taskData.projectId) {
        const project = await prisma.project.findFirst({
          where: {
            id: taskData.projectId,
            userId: req.user.id,
          },
        });

        if (!project) {
          return res.status(400).json({
            status: 'error',
            message: 'Invalid project ID',
          });
        }
      }



      // Create task
      const task = await prisma.task.create({
        data: {
          ...taskData,
          dueDate: taskData.dueDate ? new Date(taskData.dueDate) : undefined,
          scheduledTime: taskData.scheduledTime ? new Date(taskData.scheduledTime) : undefined,
          userId: req.user.id,
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              color: true,
            },
          },
          tag: {
            select: {
              id: true,
              name: true,
              icon: true,
              color: true,
            },
          },
        },
      });

      res.status(201).json({
        status: 'success',
        message: 'Task created successfully',
        data: {
          task,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get tasks for current user with filtering options
   */
  static async getTasks(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      // Parse pagination and filters
      const { page = 1, limit = 50 } = paginationSchema.parse(req.query);
      const skip = (page - 1) * limit;

      // Build filter conditions
      const where: any = {
        userId: req.user.id,
      };

      // Filter by project
      if (req.query.projectId) {
        where.projectId = req.query.projectId as string;
      }

      // Filter by status
      if (req.query.status) {
        where.status = req.query.status as string;
      }

      // Filter by tag
      if (req.query.tagId) {
        where.tagId = req.query.tagId as string;
      }



      // Get tasks
      const [tasks, total] = await Promise.all([
        prisma.task.findMany({
          where,
          include: {
            project: {
              select: {
                id: true,
                name: true,
                color: true,
              },
            },
            tag: {
              select: {
                id: true,
                name: true,
                icon: true,
                color: true,
              },
            },
          },
          orderBy: [
            { status: 'asc' },
            { dueDate: 'asc' },
            { createdAt: 'desc' },
          ],
          skip,
          take: limit,
        }),
        prisma.task.count({ where }),
      ]);

      res.json({
        status: 'success',
        data: {
          tasks,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get tasks for "Today" view
   */
  static async getTodayTasks(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      const tasks = await prisma.task.findMany({
        where: {
          userId: req.user.id,
          status: {
            in: ['TODO', 'IN_PROGRESS', 'PAUSED'],
          },
          OR: [
            {
              dueDate: {
                lte: today,
              },
            },
            {
              scheduledTime: {
                gte: new Date(new Date().setHours(0, 0, 0, 0)), // Start of today
                lte: today, // End of today
              },
            },
          ],
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              color: true,
            },
          },
          tag: {
            select: {
              id: true,
              name: true,
              icon: true,
              color: true,
            },
          },
        },
        orderBy: [
          { status: 'asc' },
          { dueDate: 'asc' },
          { scheduledTime: 'asc' },
          { createdAt: 'desc' },
        ],
      });

      res.json({
        status: 'success',
        data: {
          tasks,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get a specific task by ID
   */
  static async getTaskById(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);

      const task = await prisma.task.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              color: true,
            },
          },
          tag: {
            select: {
              id: true,
              name: true,
              icon: true,
              color: true,
            },
          },
        },
      });

      if (!task) {
        return res.status(404).json({
          status: 'error',
          message: 'Task not found',
        });
      }

      res.json({
        status: 'success',
        data: {
          task,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a task
   */
  static async updateTask(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);
      const updateData = updateTaskSchema.parse(req.body);

      // Check if task exists and belongs to user
      const existingTask = await prisma.task.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
      });

      if (!existingTask) {
        return res.status(404).json({
          status: 'error',
          message: 'Task not found',
        });
      }

      // Validate project if provided
      if (updateData.projectId) {
        const project = await prisma.project.findFirst({
          where: {
            id: updateData.projectId,
            userId: req.user.id,
          },
        });

        if (!project) {
          return res.status(400).json({
            status: 'error',
            message: 'Invalid project ID',
          });
        }
      }



      // Update task
      const updatePayload: any = { ...updateData };

      // Handle dueDate
      if (updateData.dueDate !== undefined) {
        updatePayload.dueDate = updateData.dueDate ? new Date(updateData.dueDate) : null;
      }

      // Handle scheduledTime - explicitly handle undefined to clear the field
      if (updateData.scheduledTime !== undefined) {
        updatePayload.scheduledTime = updateData.scheduledTime ? new Date(updateData.scheduledTime) : null;
      }

      const task = await prisma.task.update({
        where: { id },
        data: updatePayload,
        include: {
          project: {
            select: {
              id: true,
              name: true,
              color: true,
            },
          },
          tag: {
            select: {
              id: true,
              name: true,
              icon: true,
              color: true,
            },
          },
        },
      });

      res.json({
        status: 'success',
        message: 'Task updated successfully',
        data: {
          task,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a task
   */
  static async deleteTask(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { id } = idParamSchema.parse(req.params);

      // Check if task exists and belongs to user
      const existingTask = await prisma.task.findFirst({
        where: {
          id,
          userId: req.user.id,
        },
      });

      if (!existingTask) {
        return res.status(404).json({
          status: 'error',
          message: 'Task not found',
        });
      }

      // Delete task
      await prisma.task.delete({
        where: { id },
      });

      res.json({
        status: 'success',
        message: 'Task deleted successfully',
        data: {
          deletedTask: existingTask,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}
