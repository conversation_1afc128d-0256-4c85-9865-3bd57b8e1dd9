import { create } from 'zustand';
import { apiService, Task } from '@/services/api';

interface TaskState {
  tasks: Task[];
  todayTasks: Task[];
  currentTask: Task | null;
  isLoading: boolean;
  error: string | null;
}

interface TaskActions {
  fetchTasks: (filters?: {
    projectId?: string;
    status?: string;
    priority?: string;
  }) => Promise<void>;
  fetchTodayTasks: () => Promise<void>;
  fetchTask: (id: string) => Promise<void>;
  createTask: (data: {
    title: string;
    content?: string;
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    dueDate?: string;
    scheduledTime?: string;
    effortEstimate?: number;
    projectId?: string;
  }) => Promise<Task>;
  updateTask: (id: string, data: {
    title?: string;
    content?: string;
    status?: 'TODO' | 'IN_PROGRESS' | 'DONE' | 'ARCHIVED';
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    dueDate?: string;
    scheduledTime?: string;
    effortEstimate?: number;
    projectId?: string;
  }) => Promise<Task>;
  deleteTask: (id: string) => Promise<void>;
  setCurrentTask: (task: Task | null) => void;
  clearError: () => void;
}

type TaskStore = TaskState & TaskActions;

export const useTaskStore = create<TaskStore>((set, get) => ({
  // Initial state
  tasks: [],
  todayTasks: [],
  currentTask: null,
  isLoading: false,
  error: null,

  // Actions
  fetchTasks: async (filters) => {
    try {
      set({ isLoading: true, error: null });

      const tasks = await apiService.getTasks(filters);

      set({
        tasks,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch tasks';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  fetchTodayTasks: async () => {
    try {
      set({ isLoading: true, error: null });

      const todayTasks = await apiService.getTodayTasks();

      set({
        todayTasks,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch today tasks';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  fetchTask: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      const task = await apiService.getTask(id);

      set({
        currentTask: task,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch task';
      set({
        error: errorMessage,
        isLoading: false,
        currentTask: null,
      });
      throw error;
    }
  },

  createTask: async (data) => {
    try {
      set({ isLoading: true, error: null });

      const newTask = await apiService.createTask(data);

      set((state) => ({
        tasks: [newTask, ...state.tasks],
        isLoading: false,
        error: null,
      }));

      // Refresh today tasks if the new task might be relevant
      if (!data.dueDate || new Date(data.dueDate) <= new Date()) {
        get().fetchTodayTasks();
      }

      return newTask;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to create task';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  updateTask: async (id: string, data) => {
    try {
      set({ isLoading: true, error: null });

      const updatedTask = await apiService.updateTask(id, data);

      set((state) => ({
        tasks: state.tasks.map((task) =>
          task.id === id ? updatedTask : task
        ),
        todayTasks: state.todayTasks.map((task) =>
          task.id === id ? updatedTask : task
        ),
        currentTask: state.currentTask?.id === id ? updatedTask : state.currentTask,
        isLoading: false,
        error: null,
      }));

      // Refresh today tasks if status or due date changed
      if (data.status || data.dueDate) {
        get().fetchTodayTasks();
      }

      return updatedTask;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to update task';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  deleteTask: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      await apiService.deleteTask(id);

      set((state) => ({
        tasks: state.tasks.filter((task) => task.id !== id),
        todayTasks: state.todayTasks.filter((task) => task.id !== id),
        currentTask: state.currentTask?.id === id ? null : state.currentTask,
        isLoading: false,
        error: null,
      }));
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to delete task';
      set({
        error: errorMessage,
        isLoading: false,
      });
      throw error;
    }
  },

  setCurrentTask: (task: Task | null) => {
    set({ currentTask: task });
  },

  clearError: () => {
    set({ error: null });
  },
}));
