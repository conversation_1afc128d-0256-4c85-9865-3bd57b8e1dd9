import React, { useEffect, useState } from 'react';
import {
  Inbox,
  Trash2,
  Clock,
  Plus,
  FolderOpen,
  AlertTriangle,
  Zap,
  Minus,
  Edit3
} from 'lucide-react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
  useDraggable,
} from '@dnd-kit/core';
import { useInboxStore } from '@/store/inboxStore';
import { useProjectStore } from '@/store/projectStore';
import { useTaskStore } from '@/store/taskStore';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

import ConvertToTaskModal from '@/components/ConvertToTaskModal';
import { getRelativeTime } from '@/utils/date';
import { InboxItem } from '@/services/api';

// Priority levels with visual indicators
const priorityLevels = [
  { id: 'LOW', label: 'Low', color: 'bg-secondary-100 text-secondary-700', icon: Minus },
  { id: 'MEDIUM', label: 'Medium', color: 'bg-primary-100 text-primary-700', icon: Clock },
  { id: 'HIGH', label: 'High', color: 'bg-warning-100 text-warning-700', icon: Zap },
  { id: 'URGENT', label: 'Urgent', color: 'bg-error-100 text-error-700', icon: AlertTriangle },
];

// Simple Quick Add Component
const SimpleQuickAdd: React.FC<{ onAdd: (content: string) => void }> = ({ onAdd }) => {
  const [content, setContent] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim()) return;

    setIsAdding(true);
    await onAdd(content.trim());
    setContent('');
    setIsAdding(false);
  };

  return (
    <Card className="border-2 border-dashed border-primary-300 hover:border-primary-400 transition-colors">
      <CardContent className="p-4">
        <form onSubmit={handleSubmit} className="space-y-3">
          <Input
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="What's on your mind? (e.g., 'Call dentist', 'Buy groceries')"
            className="border-none bg-transparent text-lg placeholder:text-secondary-400"
            autoFocus
          />
          <div className="flex justify-between items-center">
            <span className="text-sm text-secondary-500">
              💡 Tip: Just type and drag to a project!
            </span>
            <Button
              type="submit"
              disabled={!content.trim() || isAdding}
              size="sm"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

// Draggable Inbox Item
const DraggableInboxItem: React.FC<{ item: InboxItem; onDelete: () => void }> = ({ item, onDelete }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: item.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`transition-all select-none ${isDragging ? 'opacity-50 rotate-2 scale-105' : ''}`}
    >
      <Card
        {...attributes}
        {...listeners}
        className="hover:shadow-md transition-shadow cursor-grab active:cursor-grabbing select-none draggable"
      >
        <CardContent className="p-4 select-none">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0 pointer-events-none select-none">
              <p className="text-secondary-900 font-medium line-clamp-2 select-none">
                {item.content}
              </p>
              <p className="text-xs text-secondary-500 mt-1 select-none">
                Added {getRelativeTime(item.createdAt)}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              onMouseDown={(e) => e.stopPropagation()}
              className="text-secondary-400 hover:text-error-600 ml-2 pointer-events-auto z-10"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Project Drop Zone
const ProjectDropZone: React.FC<{
  project: any;
  priority: any;
}> = ({ project, priority }) => {
  const dropId = `${project.id}-${priority.id}`;
  const { setNodeRef, isOver } = useDroppable({ id: dropId });

  const PriorityIcon = priority.icon;

  return (
    <div
      ref={setNodeRef}
      className={`p-4 rounded-lg border-2 border-dashed transition-all min-h-[80px] flex flex-col items-center justify-center ${
        isOver
          ? 'border-primary-500 bg-primary-50 scale-105'
          : 'border-secondary-300 hover:border-secondary-400'
      }`}
    >
      <div className="flex items-center space-x-2 mb-1">
        <div
          className="w-3 h-3 rounded-full"
          style={{ backgroundColor: project.color || '#64748b' }}
        />
        <span className="text-sm font-medium text-secondary-700">
          {project.name}
        </span>
      </div>
      <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${priority.color}`}>
        <PriorityIcon className="h-3 w-3" />
        <span>{priority.label}</span>
      </div>
      {isOver && (
        <div className="text-xs text-primary-600 mt-1 font-medium">
          Drop here!
        </div>
      )}
    </div>
  );
};

const InboxPage: React.FC = () => {
  const { inboxItems, fetchInboxItems, addInboxItem, deleteInboxItem, isLoading } = useInboxStore();
  const { projects, fetchProjects } = useProjectStore();
  const { createTask } = useTaskStore();
  const [selectedItem, setSelectedItem] = useState<InboxItem | null>(null);
  const [showConvertModal, setShowConvertModal] = useState(false);
  const [draggedItem, setDraggedItem] = useState<InboxItem | null>(null);

  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  useEffect(() => {
    fetchInboxItems();
    fetchProjects();
  }, [fetchInboxItems, fetchProjects]);

  const handleAddInboxItem = async (content: string) => {
    try {
      await addInboxItem(content);
    } catch (error) {
      console.error('Failed to add inbox item:', error);
    }
  };

  const handleDeleteItem = async (itemId: string) => {
    try {
      await deleteInboxItem(itemId);
    } catch (error) {
      console.error('Failed to delete inbox item:', error);
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    const item = inboxItems.find(item => item.id === event.active.id);
    setDraggedItem(item || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedItem(null);

    if (!over) return;

    const itemId = active.id as string;
    const dropId = over.id as string;

    // Parse drop zone ID (format: projectId-priorityId)
    const [projectId, priorityId] = dropId.split('-');

    if (!projectId || !priorityId) return;

    const item = inboxItems.find(item => item.id === itemId);
    if (!item) return;

    try {
      // Create task from inbox item
      await createTask({
        title: item.content,
        projectId: projectId,
      });

      // Delete the inbox item
      await deleteInboxItem(itemId);
    } catch (error) {
      console.error('Failed to convert inbox item to task:', error);
    }
  };

  const handleConvertSuccess = () => {
    setShowConvertModal(false);
    setSelectedItem(null);
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary-200 rounded w-1/3"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-secondary-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="p-6 space-y-6 dnd-context">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-warning-100 rounded-lg">
              <Inbox className="h-6 w-6 text-warning-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">
                Inbox
              </h1>
              <p className="text-secondary-600">
                Capture thoughts, then drag to organize
              </p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-warning-600">
              {inboxItems.length}
            </p>
            <p className="text-sm text-secondary-600">
              items to process
            </p>
          </div>
        </div>

        {/* Quick Add - Always visible */}
        <SimpleQuickAdd onAdd={handleAddInboxItem} />

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left: Inbox Items */}
          <div className="lg:col-span-1 space-y-4">
            <h2 className="text-lg font-semibold text-secondary-900 flex items-center">
              <Edit3 className="h-5 w-5 mr-2 text-warning-600" />
              Captured Items ({inboxItems.length})
            </h2>

            {inboxItems.length === 0 ? (
              <Card className="border-dashed border-2 border-secondary-300">
                <CardContent className="text-center py-8">
                  <Inbox className="h-12 w-12 text-secondary-400 mx-auto mb-3" />
                  <p className="text-secondary-600">
                    No items yet. Add something above!
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {inboxItems.map((item) => (
                  <DraggableInboxItem
                    key={item.id}
                    item={item}
                    onDelete={() => handleDeleteItem(item.id)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Right: Project Drop Zones */}
          <div className="lg:col-span-2 space-y-6">
            <h2 className="text-lg font-semibold text-secondary-900 flex items-center">
              <FolderOpen className="h-5 w-5 mr-2 text-primary-600" />
              Drag items to organize into projects
            </h2>

            {projects.length === 0 ? (
              <Card className="border-dashed border-2 border-secondary-300">
                <CardContent className="text-center py-8">
                  <FolderOpen className="h-12 w-12 text-secondary-400 mx-auto mb-3" />
                  <p className="text-secondary-600 mb-4">
                    No projects yet. Create a project first!
                  </p>
                  <Button onClick={() => window.location.href = '/projects'}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Project
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                {projects.map((project) => (
                  <Card key={project.id} className="overflow-hidden">
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: project.color || '#64748b' }}
                        />
                        <h3 className="font-semibold text-secondary-900">
                          {project.name}
                        </h3>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {priorityLevels.map((priority) => (
                          <ProjectDropZone
                            key={`${project.id}-${priority.id}`}
                            project={project}
                            priority={priority}
                          />
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Drag Overlay */}
        <DragOverlay>
          {draggedItem ? (
            <Card className="shadow-lg rotate-2 scale-105 opacity-90">
              <CardContent className="p-4">
                <p className="font-medium text-secondary-900 line-clamp-2">
                  {draggedItem.content}
                </p>
              </CardContent>
            </Card>
          ) : null}
        </DragOverlay>

        {/* Convert to Task Modal */}
        {selectedItem && (
          <ConvertToTaskModal
            isOpen={showConvertModal}
            onClose={() => {
              setShowConvertModal(false);
              setSelectedItem(null);
            }}
            inboxItem={selectedItem}
            projects={projects}
            onSuccess={handleConvertSuccess}
          />
        )}
      </div>
    </DndContext>
  );
};

export default InboxPage;
