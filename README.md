# SimpleLife: The USER-Friendly Task Management Web App

## Project Overview

SimpleLife is a task management web application meticulously designed to support users, especially those with simple executive function challenges. It aims to move beyond traditional task managers like Trello by incorporating features and UI/UX principles specifically tailored to reduce overwhelm, aid focus, capture fleeting thoughts, and provide clear, actionable organization. The core philosophy is "Clarity First, Low Friction Capture, Guided Organization."

## Problem Statement

Many individuals, particularly those who struggle with:
1.  **Task Initiation & Prioritization:** Difficulty starting tasks or switching between them inappropriately.
2.  **Working Memory & Idea Capture:** Random, important thoughts arise and are quickly forgotten if not captured immediately.
3.  **Organization & Overwhelm:** Traditional systems can become cluttered and hard to navigate, leading to avoidance.

SimpleLife addresses these by providing an intuitive system to easily capture, process, organize, and act on tasks and ideas, minimizing cognitive load and maximizing user-friendliness.

## Key Features (MVP & Core Vision)

*   **Universal Quick Add ("Brain Dump"):** Instantly capture any thought without breaking flow.
*   **Dedicated Inbox:** A processing station to clarify and assign captured thoughts.
*   **Project-Based Organization:** Group tasks into meaningful contexts.
*   **"Today" / Focus View:** A clear, prioritized list with horizontal timeline for time-based scheduling.
*   **Simplified Task Views:** Focused on timeline and list views for ADHD-friendly simplicity.
*   **USER-Friendly UI/UX:** Calm design, minimal clutter, clear actions, progressive disclosure.
*   **Reminders & Notifications:** To keep users on track.
*   **Visual Progress & Positive Reinforcement:** To motivate and encourage.

## Target Audience

*   Students, professionals, freelancers, and anyone feeling overwhelmed by tasks and ideas.
*   Users seeking a more intuitive, less cluttered alternative to existing task managers.
*   The "average American" consumer who values user-friendly design and efficiency.

## Technology Stack (Planned)

*   **Frontend:** React (with Vite), TypeScript, Tailwind CSS, Zustand (or Context API)
*   **Backend:** Node.js, Express, TypeScript, Prisma ORM
*   **Database:** PostgreSQL
*   **Authentication:** JWT (JSON Web Tokens)

## Project Status

*   [x] Planning & Design
*   [x] MVP Development
*   [x] Core Features Implementation
*   [x] ADHD-Friendly Optimizations
*   [ ] Alpha/Beta Testing
*   [ ] Release
*   [ ] Post-Release Iteration

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SimpleLife
   ```

2. **Install dependencies**
   ```bash
   npm run install:all
   ```

3. **Set up environment variables**

   **Backend (.env):**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your database credentials
   ```

   **Frontend (.env):**
   ```bash
   cp frontend/.env.example frontend/.env
   # Edit frontend/.env if needed (defaults should work for development)
   ```

4. **Set up the database**
   ```bash
   cd backend
   npx prisma migrate dev --name init
   npx prisma generate
   ```

5. **Start the development servers**
   ```bash
   # From the root directory
   npm run dev
   ```

   This will start both the backend (port 3001) and frontend (port 5173) servers.

### 🌐 Access the Application

- **Frontend:** http://localhost:5173
- **Backend API:** http://localhost:3001
- **API Health Check:** http://localhost:3001/health

## 📱 Features

### Core Features (MVP)
- ✅ **Universal Quick Add**: Instantly capture thoughts without breaking flow
- ✅ **Dedicated Inbox**: Process and organize captured items
- ✅ **Project-Based Organization**: Group tasks into meaningful contexts
- ✅ **"Today" Focus View**: Horizontal timeline with drag & drop scheduling
- ✅ **User Authentication**: Secure account management
- ✅ **USER-Friendly UI**: Calm design with minimal clutter

### Key Highlights
- **Keyboard Shortcuts**: `Ctrl+Shift+A` for Quick Add
- **Smart Processing**: Convert inbox items to organized tasks
- **Priority Levels**: Focus on what matters most (Low, Medium, High, Urgent)
- **Enhanced Filtering**: Filter tasks by Project and Priority on Today page
- **Centralized Task Creation**: All new tasks created through Inbox for simplicity
- **Simplified Structure**: No complex subtasks - focus on actionable items
- **Due Date Management**: Never miss important deadlines
- **Responsive Design**: Works on desktop, tablet, and mobile

## 🏗️ Architecture

### Backend
- **Framework**: Node.js + Express + TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT-based secure authentication
- **API**: RESTful API with comprehensive error handling

### Frontend
- **Framework**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS with custom USER-friendly theme
- **State Management**: Zustand for predictable state updates
- **Routing**: React Router for seamless navigation
- **Forms**: React Hook Form with Zod validation

### Database Schema
- **Users**: Account management and authentication
- **Projects**: Organizational contexts for tasks
- **Tasks**: Simplified task entities with essential metadata (no subtasks)
- **InboxItems**: Temporary storage for quick captures

## 🧪 Development

### Available Scripts

**Root Level:**
```bash
npm run dev          # Start both frontend and backend
npm run build        # Build both applications
npm run install:all  # Install all dependencies
```

**Backend:**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run db:migrate   # Run database migrations
npm run db:studio    # Open Prisma Studio
```

**Frontend:**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
```

### Database Management

```bash
# Create and apply migrations
npx prisma migrate dev --name migration-name

# Reset database (development only)
npx prisma migrate reset

# Generate Prisma client
npx prisma generate

# Open Prisma Studio
npx prisma studio
```

## 🚀 Deployment

### Environment Setup
1. Set up PostgreSQL database (Supabase, Neon, Railway, etc.)
2. Configure environment variables for production
3. Run database migrations: `npx prisma migrate deploy`

### Recommended Platforms
- **Frontend**: Vercel, Netlify
- **Backend**: Render, Fly.io, Railway
- **Database**: Supabase, Neon, Railway

## 📋 Project Status

- [x] **Phase 0**: Foundation & Setup
- [x] **Phase 1**: Backend Core - User Authentication
- [x] **Phase 2**: Frontend Core - Authentication UI
- [x] **Phase 3**: Core Feature - Quick Add & Inbox
- [x] **Phase 4**: Core Feature - Projects & Tasks
- [x] **Phase 5**: ADHD Optimizations - Simplified Structure & Enhanced Filtering
- [ ] **Phase 6**: MVP Polish & Refinement
- [ ] **Phase 7**: Deployment & Testing

## Vision for the Future
SimpleLife aims to be more than just a to-do list. It strives to be a reliable "second brain" that actively helps users manage their lives with less stress and more clarity. See [POSTPLAN.md](./POSTPLAN.md) for a detailed roadmap of future enhancements.

## 📚 Documentation

- [Backend Plan](./BACKENDPLAN.md) - Detailed backend development roadmap
- [Frontend Plan](./FRONTENDPLAN.md) - Frontend development guidelines
- [Database Schema](./DB.md) - Complete database design documentation
- [Full Implementation Plan](./FULLPLAN.md) - Comprehensive project roadmap
- [Future Features](./POSTPLAN.md) - Post-MVP enhancement plans

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines (coming soon) for details on how to:
- Report bugs
- Suggest features
- Submit pull requests
- Follow our coding standards

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 💬 Support

- 📧 Email: [Your Contact Email]
- 🐛 Issues: [GitHub Issues](link-to-issues)
- 💡 Discussions: [GitHub Discussions](link-to-discussions)