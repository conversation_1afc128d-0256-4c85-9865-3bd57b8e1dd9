import React, { useEffect, useState } from 'react';
import {
  Calendar,
  Clock,
  CheckCircle2,
  Sun,
  Coffee,
  Utensils,
  Moon,
  Filter,
  ChevronLeft,
  ChevronRight,
  GripVertical
} from 'lucide-react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
  useDraggable,
} from '@dnd-kit/core';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { formatDate } from '@/utils/date';
import { Task } from '@/services/api';

// Time slots configuration with visual periods
const timeSlots = [
  // Morning (6 AM - 12 PM)
  { time: '06:00', label: '6:00 AM', period: 'morning', icon: Sun, color: 'bg-yellow-50 border-yellow-200' },
  { time: '07:00', label: '7:00 AM', period: 'morning', icon: Coffee, color: 'bg-yellow-50 border-yellow-200' },
  { time: '08:00', label: '8:00 AM', period: 'morning', icon: Coffee, color: 'bg-yellow-50 border-yellow-200' },
  { time: '09:00', label: '9:00 AM', period: 'morning', icon: Sun, color: 'bg-yellow-50 border-yellow-200' },
  { time: '10:00', label: '10:00 AM', period: 'morning', icon: Sun, color: 'bg-yellow-50 border-yellow-200' },
  { time: '11:00', label: '11:00 AM', period: 'morning', icon: Sun, color: 'bg-yellow-50 border-yellow-200' },

  // Afternoon (12 PM - 6 PM)
  { time: '12:00', label: '12:00 PM', period: 'afternoon', icon: Utensils, color: 'bg-blue-50 border-blue-200' },
  { time: '13:00', label: '1:00 PM', period: 'afternoon', icon: Clock, color: 'bg-blue-50 border-blue-200' },
  { time: '14:00', label: '2:00 PM', period: 'afternoon', icon: Clock, color: 'bg-blue-50 border-blue-200' },
  { time: '15:00', label: '3:00 PM', period: 'afternoon', icon: Clock, color: 'bg-blue-50 border-blue-200' },
  { time: '16:00', label: '4:00 PM', period: 'afternoon', icon: Clock, color: 'bg-blue-50 border-blue-200' },
  { time: '17:00', label: '5:00 PM', period: 'afternoon', icon: Clock, color: 'bg-blue-50 border-blue-200' },

  // Evening (6 PM - 11 PM)
  { time: '18:00', label: '6:00 PM', period: 'evening', icon: Utensils, color: 'bg-purple-50 border-purple-200' },
  { time: '19:00', label: '7:00 PM', period: 'evening', icon: Moon, color: 'bg-purple-50 border-purple-200' },
  { time: '20:00', label: '8:00 PM', period: 'evening', icon: Moon, color: 'bg-purple-50 border-purple-200' },
  { time: '21:00', label: '9:00 PM', period: 'evening', icon: Moon, color: 'bg-purple-50 border-purple-200' },
  { time: '22:00', label: '10:00 PM', period: 'evening', icon: Moon, color: 'bg-purple-50 border-purple-200' },
];

// Draggable Task Component
const DraggableTask: React.FC<{ task: Task }> = ({ task }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`transition-all ${isDragging ? 'opacity-50 rotate-1 scale-105' : ''}`}
    >
      <Card className="hover:shadow-md transition-shadow cursor-grab active:cursor-grabbing">
        <CardContent className="p-3">
          <div className="flex items-start space-x-2">
            <div
              {...attributes}
              {...listeners}
              className="mt-1 text-secondary-400 hover:text-secondary-600 cursor-grab"
            >
              <GripVertical className="h-4 w-4" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                {task.project && (
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: task.project.color || '#64748b' }}
                  />
                )}
                <h4 className="font-medium text-secondary-900 text-sm line-clamp-1">
                  {task.title}
                </h4>
              </div>
              {task.priority && (
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  task.priority === 'URGENT' ? 'bg-error-100 text-error-700' :
                  task.priority === 'HIGH' ? 'bg-warning-100 text-warning-700' :
                  task.priority === 'MEDIUM' ? 'bg-primary-100 text-primary-700' :
                  'bg-secondary-100 text-secondary-700'
                }`}>
                  {task.priority.toLowerCase()}
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Time Slot Drop Zone
const TimeSlotDropZone: React.FC<{
  slot: typeof timeSlots[0];
  scheduledTasks: Task[];
  isToday: boolean;
}> = ({ slot, scheduledTasks, isToday }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: slot.time,
  });

  const SlotIcon = slot.icon;
  const currentHour = new Date().getHours();
  const slotHour = parseInt(slot.time.split(':')[0]);
  const isCurrentHour = isToday && currentHour === slotHour;

  return (
    <div
      ref={setNodeRef}
      className={`p-4 rounded-lg border-2 transition-all min-h-[100px] ${
        isOver
          ? 'border-primary-500 bg-primary-50 scale-102'
          : slot.color
      } ${isCurrentHour ? 'ring-2 ring-primary-400 ring-opacity-50' : ''}`}
    >
      {/* Time Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <SlotIcon className={`h-4 w-4 ${
            slot.period === 'morning' ? 'text-yellow-600' :
            slot.period === 'afternoon' ? 'text-blue-600' :
            'text-purple-600'
          }`} />
          <span className="text-sm font-medium text-secondary-700">
            {slot.label}
          </span>
        </div>
        {isCurrentHour && (
          <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse" />
        )}
      </div>

      {/* Scheduled Tasks */}
      <div className="space-y-2">
        {scheduledTasks.map((task) => (
          <div key={task.id} className="p-2 bg-white rounded border border-secondary-200">
            <div className="flex items-center space-x-2">
              {task.project && (
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: task.project.color || '#64748b' }}
                />
              )}
              <span className="text-xs font-medium text-secondary-900 line-clamp-1">
                {task.title}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Drop Indicator */}
      {isOver && (
        <div className="mt-2 p-2 border-2 border-dashed border-primary-400 rounded text-center">
          <span className="text-xs text-primary-600 font-medium">
            Drop task here
          </span>
        </div>
      )}
    </div>
  );
};

const DashboardPage: React.FC = () => {
  const { tasks, fetchTasks, updateTask, isLoading } = useTaskStore();
  const { projects, fetchProjects } = useProjectStore();
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());

  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  useEffect(() => {
    fetchTasks();
    fetchProjects();
  }, [fetchTasks, fetchProjects]);

  // Filter tasks based on selected project and current date
  const filteredTasks = tasks.filter(task => {
    if (selectedProject !== 'all' && task.projectId !== selectedProject) {
      return false;
    }
    // Only show tasks that are not scheduled for a specific time or are for today
    const taskDate = task.scheduledTime ? new Date(task.scheduledTime) : null;
    const today = currentDate.toDateString();
    return !taskDate || taskDate.toDateString() === today;
  });

  // Get unscheduled tasks (tasks without a specific time) with priority filter
  const unscheduledTasks = filteredTasks.filter(task => {
    if (task.scheduledTime || task.status === 'DONE') {
      return false;
    }
    if (selectedPriority !== 'all' && task.priority !== selectedPriority) {
      return false;
    }
    return true;
  });

  // Get scheduled tasks grouped by time slot
  const getScheduledTasksForSlot = (slotTime: string) => {
    return filteredTasks.filter(task => {
      if (!task.scheduledTime) return false;
      const taskTime = new Date(task.scheduledTime);
      const slotHour = parseInt(slotTime.split(':')[0]);
      return taskTime.getHours() === slotHour;
    });
  };

  const handleDragStart = (event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const timeSlot = over.id as string;

    try {
      // Create scheduled time for the task
      const [hours, minutes] = timeSlot.split(':');
      const scheduledDate = new Date(currentDate);
      scheduledDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      await updateTask(taskId, {
        scheduledTime: scheduledDate.toISOString(),
      });
    } catch (error) {
      console.error('Failed to schedule task:', error);
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const isToday = currentDate.toDateString() === new Date().toDateString();

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-secondary-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="p-6 space-y-6">
        {/* Header with Date Navigation */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">
              {isToday ? `Good ${getGreeting()}, plan your day` : 'Plan Your Day'}
            </h1>
            <div className="flex items-center space-x-4 mt-2">
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateDate('prev')}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-lg font-medium text-secondary-700 min-w-[200px] text-center">
                  {formatDate(currentDate.toISOString(), 'EEEE, MMMM d, yyyy')}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateDate('next')}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              {!isToday && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentDate(new Date())}
                >
                  Today
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="space-y-4">
          {/* Project Filter */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-secondary-600" />
              <span className="text-sm font-medium text-secondary-700">Project:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedProject === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedProject('all')}
              >
                All Projects
              </Button>
              {projects.map((project) => (
                <Button
                  key={project.id}
                  variant={selectedProject === project.id ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedProject(project.id)}
                  className="flex items-center space-x-2"
                >
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: project.color || '#64748b' }}
                  />
                  <span>{project.name}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Priority Filter */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-secondary-600" />
              <span className="text-sm font-medium text-secondary-700">Priority:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedPriority === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPriority('all')}
              >
                All Priorities
              </Button>
              {['URGENT', 'HIGH', 'MEDIUM', 'LOW'].map((priority) => (
                <Button
                  key={priority}
                  variant={selectedPriority === priority ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedPriority(priority)}
                  className={`${
                    priority === 'URGENT' ? 'text-error-700 border-error-300' :
                    priority === 'HIGH' ? 'text-warning-700 border-warning-300' :
                    priority === 'MEDIUM' ? 'text-primary-700 border-primary-300' :
                    'text-secondary-700 border-secondary-300'
                  }`}
                >
                  {priority.toLowerCase()}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar: Unscheduled Tasks */}
          <div className="lg:col-span-1 space-y-4">
            <Card>
              <CardHeader>
                <h2 className="text-lg font-semibold text-secondary-900 flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-primary-600" />
                  Tasks to Schedule ({unscheduledTasks.length})
                </h2>
              </CardHeader>
              <CardContent>
                {unscheduledTasks.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle2 className="h-12 w-12 text-success-400 mx-auto mb-3" />
                    <p className="text-secondary-600 text-sm">
                      All tasks are scheduled! 🎉
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <p className="text-xs text-secondary-500 mb-3">
                      💡 Drag tasks to time slots to schedule them
                    </p>
                    {unscheduledTasks.map((task) => (
                      <DraggableTask key={task.id} task={task} />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right: Time Slots Schedule */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <h2 className="text-lg font-semibold text-secondary-900 flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-primary-600" />
                  Daily Schedule
                </h2>
              </CardHeader>
              <CardContent>
                {/* Time Period Headers */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Sun className="h-5 w-5 text-yellow-600" />
                      <span className="font-semibold text-yellow-700">Morning</span>
                    </div>
                    <p className="text-xs text-secondary-600">6:00 AM - 12:00 PM</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Clock className="h-5 w-5 text-blue-600" />
                      <span className="font-semibold text-blue-700">Afternoon</span>
                    </div>
                    <p className="text-xs text-secondary-600">12:00 PM - 6:00 PM</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Moon className="h-5 w-5 text-purple-600" />
                      <span className="font-semibold text-purple-700">Evening</span>
                    </div>
                    <p className="text-xs text-secondary-600">6:00 PM - 11:00 PM</p>
                  </div>
                </div>

                {/* Time Slots Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Morning Slots */}
                  <div className="space-y-3">
                    {timeSlots.filter(slot => slot.period === 'morning').map((slot) => (
                      <TimeSlotDropZone
                        key={slot.time}
                        slot={slot}
                        scheduledTasks={getScheduledTasksForSlot(slot.time)}
                        isToday={isToday}
                      />
                    ))}
                  </div>

                  {/* Afternoon Slots */}
                  <div className="space-y-3">
                    {timeSlots.filter(slot => slot.period === 'afternoon').map((slot) => (
                      <TimeSlotDropZone
                        key={slot.time}
                        slot={slot}
                        scheduledTasks={getScheduledTasksForSlot(slot.time)}
                        isToday={isToday}
                      />
                    ))}
                  </div>

                  {/* Evening Slots */}
                  <div className="space-y-3">
                    {timeSlots.filter(slot => slot.period === 'evening').map((slot) => (
                      <TimeSlotDropZone
                        key={slot.time}
                        slot={slot}
                        scheduledTasks={getScheduledTasksForSlot(slot.time)}
                        isToday={isToday}
                      />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Drag Overlay */}
        <DragOverlay>
          {draggedTask ? (
            <Card className="shadow-lg rotate-1 scale-105 opacity-90">
              <CardContent className="p-3">
                <div className="flex items-center space-x-2">
                  {draggedTask.project && (
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: draggedTask.project.color || '#64748b' }}
                    />
                  )}
                  <span className="font-medium text-secondary-900 text-sm">
                    {draggedTask.title}
                  </span>
                </div>
              </CardContent>
            </Card>
          ) : null}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

function getGreeting(): string {
  const hour = new Date().getHours();
  if (hour < 12) return 'morning';
  if (hour < 17) return 'afternoon';
  return 'evening';
}

export default DashboardPage;
